import React, { useState, useEffect } from 'react'
import {
  Card,
  Calendar,
  Badge,
  Spin,
  Alert,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  App,
  Tooltip,
  Tag,
  Modal,
  DatePicker,
  Form
} from 'antd'
import {
  CalendarOutlined,
  ReloadOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  SyncOutlined
} from '@ant-design/icons'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'
import type {
  SectorRanking,
  SectorRankingsResponse,
  ActiveSector,
  ActiveSectorsResponse
} from '../../types/SectorCalendar'
import RankingPanel from './RankingPanel'
import CalendarSkeleton from './CalendarSkeleton'
import { sectorCalendarCache } from '../../utils/sectorCalendarCache'

const { Title, Text } = Typography

interface CalendarCellData {
  date: string
  rankings: SectorRanking[]
  hasData: boolean
  topSector?: SectorRanking
}

const SectorCalendar: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(true)
  const [calendarData, setCalendarData] = useState<Map<string, CalendarCellData>>(new Map())
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs())
  const [selectedDateData, setSelectedDateData] = useState<SectorRanking[]>([])
  const [activeSectors, setActiveSectors] = useState<ActiveSector[]>([])
  const [error, setError] = useState<string | null>(null)
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showRankingPanel, setShowRankingPanel] = useState(false)
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true)
  const [cacheHitRate, setCacheHitRate] = useState<number>(0)
  const [showBatchExportModal, setShowBatchExportModal] = useState(false)
  const [batchExportLoading, setBatchExportLoading] = useState(false)
  const [refreshingDate, setRefreshingDate] = useState<string | null>(null)
  const [batchExportForm] = Form.useForm()
  
  // 主题和消息
  const { colorScheme } = useTheme()
  const { message } = App.useApp()

  // 初始化数据加载
  useEffect(() => {
    // 设置页面标题
    document.title = '板块日历 - 股票分析系统'

    loadInitialData()

    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [])

  // 加载初始数据
  const loadInitialData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // 并行加载日历数据和活跃板块数据
      await Promise.all([
        loadCalendarData(),
        loadActiveSectors()
      ])
      
      setLastUpdateTime(dayjs().format('YYYY-MM-DD HH:mm:ss'))
    } catch (err: any) {
      console.error('加载初始数据失败:', err)
      setError(err.message || '数据加载失败')
      message.error('数据加载失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 加载日历数据（最近30天）
  const loadCalendarData = async () => {
    const endDate = dayjs()
    const startDate = endDate.subtract(30, 'day')
    const startDateStr = startDate.format('YYYY-MM-DD')
    const endDateStr = endDate.format('YYYY-MM-DD')

    try {
      // 首先尝试从缓存获取数据
      const cachedData = sectorCalendarCache.getCachedCalendarData(startDateStr, endDateStr)

      if (cachedData && cachedData.length > 0) {
        console.log('从缓存加载日历数据，数据量:', cachedData.length)
        setIsLoadingFromCache(true)

        // 使用缓存数据
        const dataMap = new Map<string, CalendarCellData>()

        cachedData.forEach((ranking: SectorRanking) => {
          const dateKey = ranking.ranking_date

          if (!dataMap.has(dateKey)) {
            dataMap.set(dateKey, {
              date: dateKey,
              rankings: [],
              hasData: false,
              topSector: undefined
            })
          }

          const cellData = dataMap.get(dateKey)!
          cellData.rankings.push(ranking)
          cellData.hasData = true

          if (ranking.ranking === 1) {
            cellData.topSector = ranking
          }
        })

        setCalendarData(dataMap)

        // 更新缓存命中率
        setCacheHitRate(prev => Math.min(prev + 0.1, 1))

        // 如果选中日期有数据，更新选中日期数据
        const selectedDateKey = selectedDate.format('YYYY-MM-DD')
        if (dataMap.has(selectedDateKey)) {
          setSelectedDateData(dataMap.get(selectedDateKey)!.rankings)
        }

        return
      }

      // 缓存未命中，从API获取数据
      console.log('缓存未命中，从API获取日历数据')
      setIsLoadingFromCache(false)

      const response: SectorRankingsResponse = await apiService.getSectorCalendarRankings({
        start_date: startDateStr,
        end_date: endDateStr,
        limit: 100 // 获取更多数据用于日历展示
      })

      if (response.data.success) {
        const dataMap = new Map<string, CalendarCellData>()

        // 按日期分组数据
        response.data.data.forEach((ranking: SectorRanking) => {
          const dateKey = ranking.ranking_date

          if (!dataMap.has(dateKey)) {
            dataMap.set(dateKey, {
              date: dateKey,
              rankings: [],
              hasData: false,
              topSector: undefined
            })
          }

          const cellData = dataMap.get(dateKey)!
          cellData.rankings.push(ranking)
          cellData.hasData = true

          // 设置排名第一的板块作为topSector
          if (ranking.ranking === 1) {
            cellData.topSector = ranking
          }
        })

        setCalendarData(dataMap)

        // 缓存数据
        sectorCalendarCache.setCachedCalendarData(response.data.data, startDateStr, endDateStr)

        // 如果选中日期有数据，更新选中日期数据
        const selectedDateKey = selectedDate.format('YYYY-MM-DD')
        if (dataMap.has(selectedDateKey)) {
          setSelectedDateData(dataMap.get(selectedDateKey)!.rankings)
        }

      } else {
        throw new Error(response.data.error || '获取日历数据失败')
      }
    } catch (err: any) {
      console.error('加载日历数据失败:', err)
      throw err
    }
  }

  // 加载活跃板块数据
  const loadActiveSectors = async () => {
    const days = 7
    const min_appearances = 2

    try {
      // 首先尝试从缓存获取数据
      const cachedData = sectorCalendarCache.getCachedActiveSectors(days, min_appearances)

      if (cachedData && cachedData.length > 0) {
        console.log('从缓存加载活跃板块数据，数据量:', cachedData.length)
        setActiveSectors(cachedData)
        setCacheHitRate(prev => Math.min(prev + 0.1, 1))
        return
      }

      // 缓存未命中，从API获取数据
      console.log('缓存未命中，从API获取活跃板块数据')

      const response: ActiveSectorsResponse = await apiService.getActiveSectors({
        days,
        min_appearances
      })

      if (response.data.success) {
        setActiveSectors(response.data.data)

        // 缓存数据
        sectorCalendarCache.setCachedActiveSectors(response.data.data, days, min_appearances)
      } else {
        console.warn('获取活跃板块数据失败:', response.data.error)
      }
    } catch (err: any) {
      console.error('加载活跃板块数据失败:', err)
      // 活跃板块数据失败不影响主要功能，只记录警告
    }
  }

  // 手动刷新数据
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // 清理缓存以强制从API获取最新数据
      sectorCalendarCache.clearAll()
      setCacheHitRate(0)

      // 重新收集最近7天的数据，确保获取最新的收盘数据
      const refreshPromises = []
      const today = dayjs()

      for (let i = 0; i < 7; i++) {
        const targetDate = today.subtract(i, 'day')
        const targetDateStr = targetDate.format('YYYY-MM-DD')

        // 只在交易日重新收集数据
        if (targetDate.day() !== 0 && targetDate.day() !== 6) { // 排除周末
          refreshPromises.push(
            apiService.triggerSectorCalendarCollection({
              target_date: targetDateStr,
              top_n: 10
            }).catch(err => {
              console.warn(`重新收集 ${targetDateStr} 数据失败:`, err)
              return null
            })
          )
        }
      }

      // 等待所有数据收集完成
      if (refreshPromises.length > 0) {
        console.log(`正在重新收集最近 ${refreshPromises.length} 个交易日的数据...`)
        await Promise.all(refreshPromises)
        console.log('数据重新收集完成')
      }

      // 重新加载页面数据
      await loadInitialData()
      message.success('数据刷新成功，已重新获取最新收盘数据')
    } catch (err: any) {
      console.error('数据刷新失败:', err)
      message.error('数据刷新失败，请稍后重试')
    } finally {
      setIsRefreshing(false)
    }
  }

  // 刷新特定日期的数据
  const handleRefreshDate = async (date: Dayjs) => {
    const dateStr = date.format('YYYY-MM-DD')
    setRefreshingDate(dateStr)

    try {
      console.log(`开始刷新 ${dateStr} 的数据...`)

      // 检查是否为交易日（排除周末）
      if (date.day() === 0 || date.day() === 6) {
        message.warning('周末不是交易日，无需刷新数据')
        return
      }

      // 触发数据重新收集
      const response = await apiService.triggerSectorCalendarCollection({
        target_date: dateStr,
        top_n: 10
      })

      if (response.data.success) {
        // 清理相关缓存
        sectorCalendarCache.clearAll()

        // 重新加载日历数据
        await loadCalendarData()

        // 如果当前选中的就是这个日期，更新选中日期数据
        if (selectedDate.format('YYYY-MM-DD') === dateStr) {
          const dataMap = new Map<string, { rankings: SectorRanking[] }>()
          calendarData.forEach((rankings, date) => {
            dataMap.set(date, { rankings })
          })

          if (dataMap.has(dateStr)) {
            setSelectedDateData(dataMap.get(dateStr)!.rankings)
          }
        }

        message.success(`${dateStr} 数据刷新成功`)
      } else {
        throw new Error(response.data.error || '数据收集失败')
      }
    } catch (err: any) {
      console.error(`刷新 ${dateStr} 数据失败:`, err)
      message.error(`刷新 ${dateStr} 数据失败: ${err.message}`)
    } finally {
      setRefreshingDate(null)
    }
  }

  // 批量导出数据
  const handleBatchExport = async (values: { dateRange: [Dayjs, Dayjs] }) => {
    setBatchExportLoading(true)
    try {
      const [startDate, endDate] = values.dateRange
      const startDateStr = startDate.format('YYYY-MM-DD')
      const endDateStr = endDate.format('YYYY-MM-DD')

      // 获取指定日期范围的数据
      const response: SectorRankingsResponse = await apiService.getSectorRankings({
        start_date: startDateStr,
        end_date: endDateStr,
        limit: 1000 // 获取所有数据
      })

      if (response.data.success && response.data.data.length > 0) {
        // 准备导出数据
        const exportData = response.data.data.map(item => ({
          日期: item.ranking_date,
          排名: item.ranking,
          板块代码: item.sector_code,
          板块名称: item.sector_name,
          '涨跌幅(%)': item.price_change_pct?.toFixed(2) || '',
          收盘价: item.close_price?.toFixed(2) || '',
          '涨跌额': item.price_change?.toFixed(2) || '',
          成交量: item.volume || '',
          '成交额': item.turnover?.toFixed(2) || '',
          连续上涨天数: item.consecutive_up_days || '',
          '5日新高': item.is_new_high_5d ? '是' : '否',
          '20日新高': item.is_new_high_20d ? '是' : '否',
          领涨股票: item.leading_stock_name || '',
          领涨股票代码: item.leading_stock_code || '',
          领涨股票价格: item.leading_stock_price?.toFixed(2) || '',
          '领涨股票涨跌幅(%)': item.leading_stock_change_pct?.toFixed(2) || ''
        }))

        // 生成CSV内容
        const headers = Object.keys(exportData[0])
        const csvContent = [
          headers.join(','),
          ...exportData.map(row =>
            headers.map(header => {
              const value = row[header as keyof typeof row]
              return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
            }).join(',')
          )
        ].join('\n')

        // 创建并下载文件
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `板块排名数据_${startDateStr}_${endDateStr}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        message.success(`成功导出 ${exportData.length} 条数据`)
        setShowBatchExportModal(false)
        batchExportForm.resetFields()
      } else {
        message.warning('选择的日期范围内没有数据')
      }
    } catch (err: any) {
      console.error('批量导出失败:', err)
      message.error('导出失败，请稍后重试')
    } finally {
      setBatchExportLoading(false)
    }
  }

  // 日期选择处理
  const handleDateSelect = (date: Dayjs) => {
    setSelectedDate(date)
    const dateKey = date.format('YYYY-MM-DD')
    const cellData = calendarData.get(dateKey)

    if (cellData && cellData.hasData) {
      setSelectedDateData(cellData.rankings)
      // 如果有数据，自动打开详情面板
      setShowRankingPanel(true)
    } else {
      setSelectedDateData([])
      setShowRankingPanel(false)
    }

    // 处理单元格样式
    handleCellClick(date)
  }

  // 日历单元格渲染
  const cellRender = (value: Dayjs) => {
    const dateKey = value.format('YYYY-MM-DD')
    const cellData = calendarData.get(dateKey)
    const isRefreshing = refreshingDate === dateKey

    // 右键菜单处理
    const handleContextMenu = (e: React.MouseEvent) => {
      e.preventDefault()

      // 检查是否为交易日
      if (value.day() === 0 || value.day() === 6) {
        message.info('周末不是交易日')
        return
      }

      // 显示确认对话框
      Modal.confirm({
        title: '刷新数据',
        content: `确定要重新获取 ${dateKey} 的板块数据吗？这将从AkShare重新获取该日期的收盘数据。`,
        okText: '确定刷新',
        cancelText: '取消',
        onOk: () => handleRefreshDate(value),
        okButtonProps: {
          loading: isRefreshing
        }
      })
    }

    if (!cellData || !cellData.hasData) {
      return (
        <div
          className="calendar-cell-content"
          style={{ minHeight: '20px', position: 'relative' }}
          onContextMenu={handleContextMenu}
          title="右键点击可刷新该日期数据"
        >
          {isRefreshing && (
            <SyncOutlined
              spin
              style={{
                color: colorScheme.primary,
                fontSize: '12px',
                position: 'absolute',
                top: '2px',
                right: '2px'
              }}
            />
          )}
        </div>
      )
    }

    const { rankings, topSector } = cellData
    const rankingCount = rankings.length

    return (
      <div
        className="calendar-cell-content"
        style={{ position: 'relative' }}
        onContextMenu={handleContextMenu}
        title="右键点击可刷新该日期数据"
      >
        {isRefreshing && (
          <SyncOutlined
            spin
            style={{
              color: colorScheme.primary,
              fontSize: '10px',
              position: 'absolute',
              top: '2px',
              right: '2px',
              zIndex: 1
            }}
          />
        )}
        <Badge
          count={rankingCount}
          size="small"
          style={{
            backgroundColor: colorScheme.primary,
            color: colorScheme.onPrimary
          }}
        />
        {topSector && (
          <div className="top-sector-info">
            <Tooltip
              title={`${topSector.sector_name}: ${topSector.price_change_pct?.toFixed(2)}%`}
              placement="top"
            >
              <Tag
                size="small"
                color={topSector.price_change_pct && topSector.price_change_pct > 0 ? 'red' : 'green'}
                style={{
                  fontSize: '10px',
                  margin: '2px 0',
                  maxWidth: '60px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                }}
              >
                {topSector.sector_name}
              </Tag>
            </Tooltip>
          </div>
        )}
      </div>
    )
  }

  // 处理日历单元格点击（添加has-data类名）
  const handleCellClick = (date: Dayjs) => {
    const dateKey = date.format('YYYY-MM-DD')
    const cellData = calendarData.get(dateKey)

    // 为有数据的单元格添加特殊样式类
    setTimeout(() => {
      const cells = document.querySelectorAll('.ant-picker-cell')
      cells.forEach(cell => {
        cell.classList.remove('has-data')
        const cellDate = cell.getAttribute('title')
        if (cellDate && calendarData.has(cellDate)) {
          cell.classList.add('has-data')
        }
      })
    }, 100)
  }

  // 获取价格变化颜色
  const getPriceChangeColor = (change?: number) => {
    if (!change) return colorScheme.onSurface
    return change > 0 ? '#ff4d4f' : change < 0 ? '#52c41a' : colorScheme.onSurface
  }

  // 获取价格变化图标
  const getPriceChangeIcon = (change?: number) => {
    if (!change) return null
    return change > 0 ? <RiseOutlined /> : <FallOutlined />
  }

  if (loading) {
    return <CalendarSkeleton />
  }

  if (error) {
    return (
      <Alert
        message="数据加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={handleRefresh}>
            重试
          </Button>
        }
        style={{ 
          margin: '20px',
          background: colorScheme.errorContainer,
          borderColor: colorScheme.error
        }}
      />
    )
  }

  return (
    <div 
      className="sector-calendar-container"
      style={{ 
        padding: '24px',
        background: colorScheme.background,
        minHeight: '100vh'
      }}
    >
      {/* 页面标题和操作栏 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space align="center">
            <CalendarOutlined style={{ fontSize: '24px', color: colorScheme.primary }} />
            <Title level={2} style={{ margin: 0, color: colorScheme.onBackground }}>
              板块日历
            </Title>
            <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
              追踪每日活跃板块动向
            </Text>
          </Space>
        </Col>
        <Col>
          <Space>
            <div style={{ textAlign: 'right' }}>
              <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant, fontSize: '12px' }}>
                最后更新: {lastUpdateTime}
              </Text>
              {cacheHitRate > 0 && (
                <div>
                  <Text type="secondary" style={{ color: colorScheme.primary, fontSize: '11px' }}>
                    缓存命中率: {(cacheHitRate * 100).toFixed(0)}%
                  </Text>
                </div>
              )}
              {isLoadingFromCache && (
                <div>
                  <Text type="secondary" style={{ color: colorScheme.secondary, fontSize: '11px' }}>
                    ⚡ 缓存加速
                  </Text>
                </div>
              )}
            </div>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => setShowBatchExportModal(true)}
              style={{
                borderColor: colorScheme.outline,
                color: colorScheme.primary
              }}
            >
              批量导出
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={isRefreshing}
              style={{
                borderColor: colorScheme.outline,
                color: colorScheme.primary
              }}
            >
              刷新数据
            </Button>
          </Space>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 左侧：日历视图 */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <CalendarOutlined />
                <span>板块活跃日历</span>
                <Tooltip title="点击日期查看当日排名详情">
                  <InfoCircleOutlined style={{ color: colorScheme.onSurfaceVariant }} />
                </Tooltip>
              </Space>
            }
            style={{
              background: colorScheme.surface,
              borderColor: colorScheme.outline
            }}
            styles={{
              header: {
                background: colorScheme.surfaceVariant,
                borderBottomColor: colorScheme.outline,
                color: colorScheme.onSurface
              },
              body: {
                background: colorScheme.surface
              }
            }}
          >
            <Calendar
              value={selectedDate}
              onSelect={handleDateSelect}
              cellRender={cellRender}
              style={{
                background: colorScheme.surface
              }}
            />
          </Card>
        </Col>

        {/* 右侧：统计信息和活跃板块 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 统计卡片 */}
            <Card
              title={
                <Space>
                  <TrophyOutlined />
                  <span>数据统计</span>
                </Space>
              }
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline
              }}
              styles={{
                header: {
                  background: colorScheme.surfaceVariant,
                  borderBottomColor: colorScheme.outline,
                  color: colorScheme.onSurface
                }
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="有数据天数"
                    value={calendarData.size}
                    suffix="天"
                    valueStyle={{ color: colorScheme.primary }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="活跃板块"
                    value={activeSectors.length}
                    suffix="个"
                    valueStyle={{ color: colorScheme.secondary }}
                  />
                </Col>
              </Row>
            </Card>

            {/* 活跃板块列表 */}
            <Card
              title={
                <Space>
                  <RiseOutlined />
                  <span>连续活跃板块</span>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    (近7天出现≥2次)
                  </Text>
                </Space>
              }
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline
              }}
              styles={{
                header: {
                  background: colorScheme.surfaceVariant,
                  borderBottomColor: colorScheme.outline,
                  color: colorScheme.onSurface
                }
              }}
            >
              {activeSectors.length > 0 ? (
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  {activeSectors.slice(0, 8).map((sector, index) => (
                    <div 
                      key={sector.sector_code}
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '8px 12px',
                        background: index % 2 === 0 ? colorScheme.surfaceVariant : 'transparent',
                        borderRadius: '6px'
                      }}
                    >
                      <div>
                        <Text strong style={{ color: colorScheme.onSurface }}>
                          {sector.sector_name}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          平均排名: {sector.avg_ranking.toFixed(1)}
                        </Text>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <Tag color="blue" size="small">
                          {sector.appearances}次
                        </Tag>
                        <br />
                        <Text style={{ fontSize: '12px', color: colorScheme.primary }}>
                          最佳: #{sector.best_ranking}
                        </Text>
                      </div>
                    </div>
                  ))}
                </Space>
              ) : (
                <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
                  暂无连续活跃板块数据
                </Text>
              )}
            </Card>
          </Space>
        </Col>
      </Row>

      {/* 选中日期详情 */}
      {selectedDateData.length > 0 && (
        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card
              title={
                <Space>
                  <CalendarOutlined />
                  <span>{selectedDate.format('YYYY年MM月DD日')} 板块排名</span>
                  <Tag color="blue">{selectedDateData.length}个板块</Tag>
                </Space>
              }
              extra={
                <Button
                  type="primary"
                  size="small"
                  onClick={() => setShowRankingPanel(true)}
                  style={{
                    background: colorScheme.primary,
                    borderColor: colorScheme.primary
                  }}
                >
                  查看详情
                </Button>
              }
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline
              }}
              styles={{
                header: {
                  background: colorScheme.surfaceVariant,
                  borderBottomColor: colorScheme.outline,
                  color: colorScheme.onSurface
                }
              }}
            >
              <Row gutter={[16, 16]}>
                {selectedDateData.map((ranking) => (
                  <Col xs={24} sm={12} md={8} lg={6} key={ranking.id}>
                    <Card
                      size="small"
                      style={{
                        background: colorScheme.surfaceVariant,
                        borderColor: colorScheme.outline,
                        height: '120px'
                      }}
                      styles={{
                        body: {
                          padding: '12px',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between'
                        }
                      }}
                    >
                      <div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <Tag
                            color={ranking.ranking <= 3 ? 'gold' : 'blue'}
                            style={{ margin: 0 }}
                          >
                            #{ranking.ranking}
                          </Tag>
                          {ranking.price_change_pct && (
                            <Text
                              style={{
                                color: getPriceChangeColor(ranking.price_change_pct),
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}
                            >
                              {getPriceChangeIcon(ranking.price_change_pct)}
                              {ranking.price_change_pct > 0 ? '+' : ''}
                              {ranking.price_change_pct.toFixed(2)}%
                            </Text>
                          )}
                        </div>

                        <Tooltip title={ranking.sector_name}>
                          <Text
                            strong
                            style={{
                              color: colorScheme.onSurface,
                              fontSize: '14px',
                              display: 'block',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {ranking.sector_name}
                          </Text>
                        </Tooltip>
                      </div>

                      <div style={{ fontSize: '12px', color: colorScheme.onSurfaceVariant }}>
                        {ranking.close_price && (
                          <div>价格: ¥{ranking.close_price.toFixed(2)}</div>
                        )}
                        {ranking.volume && (
                          <div>成交量: {(ranking.volume / 10000).toFixed(0)}万</div>
                        )}
                        {ranking.consecutive_up_days !== undefined && ranking.consecutive_up_days > 0 && (
                          <div style={{ color: colorScheme.primary }}>
                            连涨{ranking.consecutive_up_days}天
                          </div>
                        )}
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
        </Row>
      )}

      {/* 选中日期无数据提示 */}
      {selectedDateData.length === 0 && selectedDate && (
        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline,
                textAlign: 'center'
              }}
            >
              <Space direction="vertical" size="middle">
                <InfoCircleOutlined
                  style={{
                    fontSize: '48px',
                    color: colorScheme.onSurfaceVariant
                  }}
                />
                <div>
                  <Text style={{ color: colorScheme.onSurface, fontSize: '16px' }}>
                    {selectedDate.format('YYYY年MM月DD日')} 暂无板块排名数据
                  </Text>
                  <br />
                  <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
                    可能是非交易日或数据尚未收集
                  </Text>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      )}

      {/* 排名详情面板 */}
      <RankingPanel
        visible={showRankingPanel}
        onClose={() => setShowRankingPanel(false)}
        date={selectedDate.format('YYYY-MM-DD')}
        rankings={selectedDateData}
        title={`${selectedDate.format('YYYY年MM月DD日')} 板块排名详情`}
      />

      {/* 批量导出模态框 */}
      <Modal
        title="批量导出板块排名数据"
        open={showBatchExportModal}
        onCancel={() => {
          setShowBatchExportModal(false)
          batchExportForm.resetFields()
        }}
        footer={null}
        width={500}
      >
        <Form
          form={batchExportForm}
          layout="vertical"
          onFinish={handleBatchExport}
          initialValues={{
            dateRange: [dayjs().subtract(7, 'day'), dayjs()]
          }}
        >
          <Form.Item
            name="dateRange"
            label="选择日期范围"
            rules={[{ required: true, message: '请选择日期范围' }]}
          >
            <DatePicker.RangePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder={['开始日期', '结束日期']}
              disabledDate={(current) => {
                // 禁用未来日期
                return current && current > dayjs().endOf('day')
              }}
            />
          </Form.Item>

          <div style={{ marginBottom: '16px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              导出文件将包含所选日期范围内的所有板块排名数据，包括：日期、排名、板块信息、价格数据、领涨股票信息等。
            </Text>
          </div>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setShowBatchExportModal(false)
                  batchExportForm.resetFields()
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={batchExportLoading}
                icon={<DownloadOutlined />}
              >
                导出数据
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 自定义样式 */}
      <style>{`
        .calendar-cell-content {
          position: relative;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
        
        .top-sector-info {
          margin-top: 2px;
          width: 100%;
          display: flex;
          justify-content: center;
        }
        
        .ant-picker-calendar-date-content {
          height: 60px !important;
        }
        
        .ant-picker-calendar .ant-picker-cell-selected .ant-picker-calendar-date {
          background: ${colorScheme.primaryContainer} !important;
          border-color: ${colorScheme.primary} !important;
        }
        
        .ant-picker-calendar .ant-picker-cell-today .ant-picker-calendar-date {
          border-color: ${colorScheme.secondary} !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .ant-picker-calendar-date-content {
            height: 40px !important;
          }

          .calendar-cell-content .ant-badge {
            transform: scale(0.8);
          }

          .top-sector-info .ant-tag {
            font-size: 8px !important;
            max-width: 40px !important;
          }
        }

        /* 日历单元格悬停效果 */
        .ant-picker-calendar .ant-picker-cell:hover .ant-picker-calendar-date {
          background: ${colorScheme.surfaceVariant} !important;
          transition: background 0.2s ease;
        }

        /* 有数据的日期高亮 */
        .ant-picker-calendar .ant-picker-cell.has-data .ant-picker-calendar-date {
          border: 2px solid ${colorScheme.primary}20 !important;
          background: ${colorScheme.primaryContainer}40 !important;
        }
      `}</style>
    </div>
  )
}

export default SectorCalendar
