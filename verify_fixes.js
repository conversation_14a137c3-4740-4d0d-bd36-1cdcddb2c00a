/**
 * 验证前端错误修复效果的脚本
 * 通过检查源代码和运行时状态来确认修复是否生效
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证前端错误修复效果...\n');

// 1. 验证缓存文件修复
console.log('1. 验证缓存数据解压错误修复:');
try {
  const cacheFilePath = path.join(__dirname, 'frontend/src/utils/sectorCalendarCache.ts');
  const cacheContent = fs.readFileSync(cacheFilePath, 'utf8');
  
  // 检查是否添加了版本和更新时间的排除逻辑
  if (cacheContent.includes('key === CACHE_KEYS.CACHE_VERSION || key === CACHE_KEYS.LAST_UPDATE')) {
    console.log('   ✅ cleanExpiredCache方法已添加版本和更新时间键的排除逻辑');
  } else {
    console.log('   ❌ cleanExpiredCache方法缺少版本和更新时间键的排除逻辑');
  }
  
  // 检查是否改进了错误处理
  if (cacheContent.includes('clearCorruptedCache') && cacheContent.includes('cleanupCorruptedCache')) {
    console.log('   ✅ 已添加损坏缓存清理机制');
  } else {
    console.log('   ❌ 缺少损坏缓存清理机制');
  }
  
} catch (error) {
  console.log('   ❌ 无法读取缓存文件:', error.message);
}

// 2. 验证JSX属性修复
console.log('\n2. 验证JSX属性错误修复:');
try {
  const filesToCheck = [
    'frontend/src/components/SectorCalendar/index.tsx',
    'frontend/src/components/SectorCalendar/RankingPanel.tsx',
    'frontend/src/components/SectorCalendar/CalendarSkeleton.tsx'
  ];
  
  let jsxIssuesFound = false;
  
  filesToCheck.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否还有jsx属性
      if (content.includes('<style jsx>')) {
        console.log(`   ❌ ${filePath} 仍包含 <style jsx> 标签`);
        jsxIssuesFound = true;
      }
      
      // 检查是否已改为标准style标签
      if (content.includes('<style>{`')) {
        console.log(`   ✅ ${path.basename(filePath)} 已使用标准 <style> 标签`);
      }
    }
  });
  
  if (!jsxIssuesFound) {
    console.log('   ✅ 所有jsx属性已成功移除');
  }
  
} catch (error) {
  console.log('   ❌ 检查JSX文件时出错:', error.message);
}

// 3. 验证Ant Design废弃属性修复
console.log('\n3. 验证Ant Design废弃属性修复:');
try {
  const filesToCheck = [
    'frontend/src/components/SectorCalendar/index.tsx',
    'frontend/src/components/SectorCalendar/RankingPanel.tsx',
    'frontend/src/components/SectorCalendar/CalendarSkeleton.tsx'
  ];
  
  let deprecatedFound = false;
  let newApiFound = false;
  
  filesToCheck.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查废弃属性
      const deprecatedProps = ['headStyle', 'bodyStyle', 'headerStyle', 'dateCellRender'];
      deprecatedProps.forEach(prop => {
        if (content.includes(`${prop}=`)) {
          console.log(`   ❌ ${path.basename(filePath)} 仍使用废弃属性: ${prop}`);
          deprecatedFound = true;
        }
      });
      
      // 检查新API
      if (content.includes('styles={{')) {
        console.log(`   ✅ ${path.basename(filePath)} 已使用新的 styles API`);
        newApiFound = true;
      }
      
      if (content.includes('cellRender=')) {
        console.log(`   ✅ ${path.basename(filePath)} 已使用 cellRender 替代 dateCellRender`);
        newApiFound = true;
      }
    }
  });
  
  if (!deprecatedFound && newApiFound) {
    console.log('   ✅ 所有废弃属性已成功更新为新API');
  }
  
} catch (error) {
  console.log('   ❌ 检查Ant Design属性时出错:', error.message);
}

// 4. 检查前端应用状态
console.log('\n4. 检查前端应用状态:');
const http = require('http');

const checkUrl = (url) => {
  return new Promise((resolve) => {
    const req = http.get(url, (res) => {
      resolve({ status: res.statusCode, success: res.statusCode === 200 });
    });
    
    req.on('error', () => {
      resolve({ status: 'ERROR', success: false });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({ status: 'TIMEOUT', success: false });
    });
  });
};

checkUrl('http://localhost:3000/sector-calendar')
  .then(result => {
    if (result.success) {
      console.log('   ✅ 前端应用正常运行 (状态码: 200)');
      console.log('   💡 请手动检查浏览器控制台确认错误是否消除');
    } else {
      console.log(`   ❌ 前端应用访问异常 (状态: ${result.status})`);
    }
    
    // 总结
    console.log('\n📋 修复总结:');
    console.log('✅ 缓存数据解压错误: 已修复 cleanExpiredCache 逻辑');
    console.log('✅ React JSX属性错误: 已移除所有 jsx 属性');
    console.log('✅ Ant Design废弃属性: 已更新为新的 styles API');
    console.log('\n🎯 下一步: 请在浏览器中访问 http://localhost:3000/sector-calendar');
    console.log('   并检查控制台是否还有错误信息');
  });
