/**
 * 前端错误修复验证脚本
 * 用于验证板块日历页面的控制台错误是否已修复
 */

const puppeteer = require('puppeteer');

async function testFrontendFixes() {
  console.log('🚀 开始测试前端错误修复效果...\n');

  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口
      devtools: true,  // 打开开发者工具
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // 监听控制台消息
    const consoleMessages = [];
    const errors = [];
    const warnings = [];

    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push({
        type: msg.type(),
        text: text,
        timestamp: new Date().toISOString()
      });

      if (msg.type() === 'error') {
        errors.push(text);
      } else if (msg.type() === 'warning') {
        warnings.push(text);
      }
    });

    // 监听页面错误
    page.on('pageerror', error => {
      errors.push(`页面错误: ${error.message}`);
    });

    console.log('📱 导航到板块日历页面...');
    await page.goto('http://localhost:3000/sector-calendar', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    // 等待页面完全加载
    console.log('⏳ 等待页面加载完成...');
    await page.waitForTimeout(5000);

    // 检查页面是否正常渲染
    const pageTitle = await page.title();
    console.log(`📄 页面标题: ${pageTitle}`);

    // 检查关键元素是否存在
    const calendarExists = await page.$('.ant-picker-calendar') !== null;
    const cardExists = await page.$('.ant-card') !== null;
    
    console.log(`📅 日历组件存在: ${calendarExists ? '✅' : '❌'}`);
    console.log(`🃏 卡片组件存在: ${cardExists ? '✅' : '❌'}`);

    // 等待更多交互
    console.log('🔄 测试页面交互...');
    
    // 尝试点击日历中的日期
    try {
      await page.click('.ant-picker-cell');
      console.log('📅 日历点击测试: ✅');
    } catch (e) {
      console.log('📅 日历点击测试: ❌', e.message);
    }

    // 等待更多控制台消息
    await page.waitForTimeout(3000);

    // 分析控制台消息
    console.log('\n📊 控制台消息分析:');
    console.log(`总消息数: ${consoleMessages.length}`);
    console.log(`错误数: ${errors.length}`);
    console.log(`警告数: ${warnings.length}`);

    // 检查特定的已修复错误
    const fixedErrors = {
      cacheDecompressionError: false,
      jsxAttributeError: false,
      antdDeprecatedWarnings: false
    };

    // 检查缓存解压错误
    const cacheErrors = errors.filter(err => 
      err.includes('数据解压失败') || 
      err.includes('SyntaxError: Unexpected non-whitespace character')
    );
    fixedErrors.cacheDecompressionError = cacheErrors.length === 0;

    // 检查JSX属性错误
    const jsxErrors = errors.filter(err => 
      err.includes('Received true for a non-boolean attribute jsx')
    );
    fixedErrors.jsxAttributeError = jsxErrors.length === 0;

    // 检查Ant Design废弃属性警告
    const antdWarnings = warnings.filter(warn => 
      warn.includes('headStyle') || 
      warn.includes('bodyStyle') || 
      warn.includes('dateCellRender') ||
      warn.includes('headerStyle')
    );
    fixedErrors.antdDeprecatedWarnings = antdWarnings.length === 0;

    console.log('\n🔍 修复验证结果:');
    console.log(`缓存数据解压错误修复: ${fixedErrors.cacheDecompressionError ? '✅' : '❌'}`);
    console.log(`React JSX属性错误修复: ${fixedErrors.jsxAttributeError ? '✅' : '❌'}`);
    console.log(`Ant Design废弃属性警告修复: ${fixedErrors.antdDeprecatedWarnings ? '✅' : '❌'}`);

    // 显示剩余的错误和警告
    if (errors.length > 0) {
      console.log('\n❌ 剩余错误:');
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (warnings.length > 0) {
      console.log('\n⚠️ 剩余警告:');
      warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }

    // 总结
    const allFixed = Object.values(fixedErrors).every(fixed => fixed);
    console.log(`\n🎯 总体修复状态: ${allFixed ? '✅ 全部修复成功' : '❌ 仍有问题需要解决'}`);

    return {
      success: allFixed,
      errors: errors.length,
      warnings: warnings.length,
      fixedErrors,
      pageRendered: calendarExists && cardExists
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 运行测试
if (require.main === module) {
  testFrontendFixes()
    .then(result => {
      console.log('\n📋 测试完成，结果:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = testFrontendFixes;
