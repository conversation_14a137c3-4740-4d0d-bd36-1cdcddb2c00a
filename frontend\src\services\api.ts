import axios from 'axios'
import type {
  SectorRankingsParams,
  ActiveSectorsParams,
  CollectionTriggerParams,
  SectorRankingsResponse,
  ActiveSectorsResponse,
  CollectionTriggerResponse
} from '../types/SectorCalendar'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 60000, // 增加到60秒，适应故障转移机制的重试时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 创建用于数据更新的axios实例（更长的超时时间）
const updateApi = axios.create({
  baseURL: '/api',
  timeout: 420000, // 7分钟超时，为智能更新提供充足的安全缓冲时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 创建用于大数据量获取的axios实例（更长的超时时间）
const longTimeoutApi = axios.create({
  baseURL: '/api',
  timeout: 300000, // 5分钟超时，适用于86个板块数据获取
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('发送API请求:', config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.config.url, response.status)
    return response
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.message)
    if (error.response?.status === 404) {
      console.error('API接口不存在:', error.config?.url)
    } else if (error.response?.status >= 500) {
      console.error('服务器错误:', error.response?.data)
    }
    return Promise.reject(error)
  }
)

// 为updateApi添加拦截器
updateApi.interceptors.request.use(
  (config) => {
    console.log('发送数据更新请求:', config.url)
    return config
  },
  (error) => {
    console.error('更新请求错误:', error)
    return Promise.reject(error)
  }
)

updateApi.interceptors.response.use(
  (response) => {
    console.log('数据更新响应:', response.config.url, response.status)
    return response
  },
  (error) => {
    console.error('数据更新响应错误:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

// 为longTimeoutApi添加拦截器
longTimeoutApi.interceptors.request.use(
  (config) => {
    console.log('发送长时间数据获取请求:', config.url)
    return config
  },
  (error) => {
    console.error('长时间数据获取请求错误:', error)
    return Promise.reject(error)
  }
)

longTimeoutApi.interceptors.response.use(
  (response) => {
    console.log('长时间数据获取响应:', response.config.url, response.status)
    return response
  },
  (error) => {
    console.error('长时间数据获取响应错误:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

// API接口定义
export const apiService = {
  // 健康检查
  healthCheck: () => api.get('/health'),

  // 测试接口
  test: () => api.get('/test'),

  // 获取板块列表（支持分页）
  getSectors: (page: number = 1, perPage: number = 20, includeDetails: boolean = true, industryLevel?: string) =>
    api.get('/sectors', {
      params: {
        page,
        per_page: perPage,
        include_details: includeDetails,
        industry_level: industryLevel
      }
    }),

  // 获取申万31行业数据
  getShenwan31Industries: (page: number = 1, perPage: number = 31, includeDetails: boolean = true) =>
    api.get('/sectors/shenwan31-optimized'),

  // 获取板块详情
  getSectorDetail: (sectorCode: string, days: number = 30, analysisDays: number = 7) =>
    api.get(`/sectors/${sectorCode}`, { params: { days, analysis_days: analysisDays } }),

  // 获取板块股票列表
  getSectorStocks: (sectorCode: string) =>
    api.get(`/sectors/${sectorCode}/stocks`),

  // 批量获取股票的特殊数据表信息（N形待选和大笔买入）
  // 使用长超时API实例，因为需要查询大量股票的实时数据
  getStocksSpecialData: (stockCodes: string[]) =>
    longTimeoutApi.post('/stocks/special-data', { stock_codes: stockCodes }),

  // 更新单个板块数据
  updateSingleSector: (sectorCode: string) =>
    updateApi.post(`/sectors/${sectorCode}/update`),

  // 更新单个板块技术指标
  updateSingleSectorIndicators: (sectorCode: string) =>
    updateApi.post(`/sectors/${sectorCode}/indicators/update`),

  // 批量更新所有板块技术指标
  updateAllSectorsIndicators: () =>
    updateApi.post('/sectors/indicators/batch-update'),

  // 获取趋势分析
  getTrendAnalysis: (direction?: string, date?: string) =>
    api.get('/analysis/trend', { params: { direction, date } }),

  // 获取震荡分析
  getOscillationAnalysis: (date?: string) =>
    api.get('/analysis/oscillation', { params: { date } }),

  // 获取连续上涨分析
  getConsecutiveAnalysis: (minDays: number = 3, date?: string) =>
    api.get('/analysis/consecutive', { params: { min_days: minDays, date } }),

  // 获取创新高分析
  getNewHighAnalysis: (days: number = 5, date?: string) =>
    api.get('/analysis/new-high', { params: { days, date } }),

  // 获取市场概览统计
  getSummaryStats: (date?: string) =>
    api.get('/stats/summary', { params: { date } }),

  // 获取概念分析数据
  getConceptAnalysis: () => api.get('/concept-analysis'),

  // 获取指定概念的涨停股票列表
  getConceptStocks: (conceptName: string) =>
    api.get(`/concept-stocks/${encodeURIComponent(conceptName)}`),

  // 获取指定概念的N型待选股票列表
  getConceptNStocks: (conceptName: string) =>
    api.get(`/concept-n-stocks/${encodeURIComponent(conceptName)}`),

  // 获取系统状态
  getSystemStatus: () => api.get('/system/status'),

  // 获取调度器状态
  getSchedulerStatus: () => api.get('/scheduler/status'),

  // 手动更新数据
  updateData: (sectorCode?: string) =>
    updateApi.post('/data/update', sectorCode ? { sector_code: sectorCode } : {}),

  // 快速更新缺少分析数据的板块
  updateMissingAnalysis: () =>
    updateApi.post('/data/update-missing'),

  // 初始化数据
  initData: () => api.post('/data/init'),

  // 手动触发更新
  manualUpdate: (sectorCode?: string) =>
    api.post('/scheduler/manual-update', sectorCode ? { sector_code: sectorCode } : {}),

  // 获取86个板块实时数据（使用长超时API实例）
  getAllSectorsRealtime: () => longTimeoutApi.get('/sectors/all-realtime'),

  // 计算86个板块技术指标（基于MySQL缓存数据）
  calculateSectorsIndicators: () => updateApi.post('/sectors/calculate-indicators'),

  // 更新技术指标（智能增量更新策略 - 同步处理，300秒超时）
  updateTechnicalIndicators: () => longTimeoutApi.post('/data/update-indicators'),

  // 智能数据更新（真正的智能更新功能）
  smartDataUpdate: () => updateApi.post('/data/update', {
    update_mode: 'incremental',
    force_user_update: true  // 🔧 修复：标识为用户主动更新，在交易时间内强制获取最新数据
  }),

  // 手动刷新板块数据（根据交易时间判断）
  refreshSectorsData: (dataType: 'all' | 'shenwan31' = 'all', forceRefresh: boolean = false) =>
    updateApi.post('/sectors/refresh', {
      data_type: dataType,
      force_refresh: forceRefresh
    }),

  // 完全重载86个板块数据（同步处理，支持进度回调）
  fullReloadSectorsData: () => {
    // 创建专门用于完全重载的axios实例，支持300秒超时
    const fullReloadApi = axios.create({
      baseURL: '/api',
      timeout: 300000, // 5分钟超时
      headers: {
        'Content-Type': 'application/json',
      },
    })

    return fullReloadApi.post('/sectors/full-reload')
  },

  // === 异步任务API ===

  // 创建板块数据刷新异步任务
  createSectorsRefreshTask: (dataType: 'all' | 'shenwan31' = 'all', forceRefresh: boolean = false) =>
    api.post('/tasks/sectors/refresh', {
      data_type: dataType,
      force_refresh: forceRefresh
    }),

  // 创建技术指标更新异步任务
  createIndicatorsUpdateTask: (updateMode: 'incremental' | 'full' = 'incremental') =>
    api.post('/tasks/indicators/update', {
      update_mode: updateMode
    }),

  // 获取任务状态
  getTaskStatus: (taskId: string) =>
    api.get(`/tasks/${taskId}/status`),

  // 获取任务结果
  getTaskResult: (taskId: string) =>
    api.get(`/tasks/${taskId}/result`),

  // 取消任务
  cancelTask: (taskId: string, reason?: string) =>
    api.post(`/tasks/${taskId}/cancel`, {
      reason: reason || '用户取消'
    }),

  // 获取任务列表
  getTaskList: (limit: number = 50, status?: string) => {
    const params = new URLSearchParams({ limit: limit.toString() })
    if (status) {
      params.append('status', status)
    }
    return api.get(`/tasks/list?${params.toString()}`)
  },

  // 清理旧任务
  cleanupOldTasks: (days: number = 7) =>
    api.post('/tasks/cleanup', { days }),

  // ==================== 板块日历API接口 ====================

  // 获取板块日历排名数据
  getSectorCalendarRankings: (params?: SectorRankingsParams): Promise<SectorRankingsResponse> => {
    const queryParams = new URLSearchParams()

    if (params?.start_date) queryParams.append('start_date', params.start_date)
    if (params?.end_date) queryParams.append('end_date', params.end_date)
    if (params?.date) queryParams.append('date', params.date)
    if (params?.limit) queryParams.append('limit', params.limit.toString())

    const url = `/sector-calendar/rankings${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return api.get(url)
  },

  // 手动触发板块排名数据收集
  triggerSectorCalendarCollection: (params?: CollectionTriggerParams): Promise<CollectionTriggerResponse> => {
    return api.post('/sector-calendar/collect', params || {})
  },

  // 获取连续活跃的板块
  getActiveSectors: (params?: ActiveSectorsParams): Promise<ActiveSectorsResponse> => {
    const queryParams = new URLSearchParams()

    if (params?.days) queryParams.append('days', params.days.toString())
    if (params?.min_appearances) queryParams.append('min_appearances', params.min_appearances.toString())

    const url = `/sector-calendar/active-sectors${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return api.get(url)
  },

  // 获取板块排名数据（别名方法，用于批量导出功能）
  getSectorRankings: (params?: SectorRankingsParams): Promise<SectorRankingsResponse> => {
    // 直接调用getSectorCalendarRankings方法
    return apiService.getSectorCalendarRankings(params)
  },
}

export default api
