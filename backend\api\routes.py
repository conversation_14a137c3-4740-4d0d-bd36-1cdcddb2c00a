"""
API路由定义
"""
from flask import Blueprint, jsonify, request, Response
from datetime import datetime, date, timedelta
from typing import Dict, Any, List
import logging
import pandas as pd
import json
import time
import akshare as ak
from models import Sector, DailyQuote, TechnicalAnalysis
from services.database_service import database_service
from services.data_service import data_service
from database import db
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 创建蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

def _force_clear_sector_cache(cache_manager, table_name, sector_code):
    """强制清除特定板块的缓存数据"""
    try:
        from sqlalchemy import text

        # 删除特定板块的缓存记录
        with cache_manager.engine.connect() as conn:
            if table_name == 'historical_data_cache':
                # 清除历史数据缓存
                delete_sql = text(f"DELETE FROM {table_name} WHERE symbol = :sector_code OR cache_key LIKE :cache_pattern")
                conn.execute(delete_sql, {
                    'sector_code': sector_code,
                    'cache_pattern': f'%{sector_code}%'
                })
            elif table_name == 'realtime_quotes_cache':
                # 清除实时数据缓存
                delete_sql = text(f"DELETE FROM {table_name} WHERE symbol = :sector_code OR cache_key LIKE :cache_pattern")
                conn.execute(delete_sql, {
                    'sector_code': sector_code,
                    'cache_pattern': f'%{sector_code}%'
                })
            elif table_name == 'sector_data_cache':
                # 清除板块数据缓存
                delete_sql = text(f"DELETE FROM {table_name} WHERE sector_code = :sector_code OR cache_key LIKE :cache_pattern")
                conn.execute(delete_sql, {
                    'sector_code': sector_code,
                    'cache_pattern': f'%{sector_code}%'
                })

            conn.commit()
            logger.debug(f"成功清除 {table_name} 中板块 {sector_code} 的缓存")

    except Exception as e:
        logger.warning(f"清除 {table_name} 中板块 {sector_code} 缓存失败: {e}")
        # 降级到清除所有过期缓存
        try:
            cache_manager.clear_expired_cache()
        except Exception as e2:
            logger.error(f"降级清除过期缓存也失败: {e2}")

@api_bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'stock_analysis_backend'
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors', methods=['GET'])
def get_sectors():
    """获取板块列表（支持分页）"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)  # 默认每页20个
        include_details = request.args.get('include_details', 'true').lower() == 'true'
        # 新增：行业级别过滤参数
        industry_level = request.args.get('industry_level', None)

        logger.info(f"开始获取板块列表 - 页码: {page}, 每页: {per_page}, 包含详情: {include_details}, 行业级别: {industry_level}")

        # 构建WHERE条件
        where_conditions = ["s.is_active = 1"]
        count_where_conditions = ["is_active = 1"]  # 用于COUNT查询的条件（不带表别名）
        if industry_level:
            where_conditions.append(f"s.industry_level = '{industry_level}'")
            count_where_conditions.append(f"industry_level = '{industry_level}'")
        where_clause = " AND ".join(where_conditions)
        count_where_clause = " AND ".join(count_where_conditions)

        # 使用优化的SQL查询，支持分页
        if include_details:
            # 使用JOIN查询一次性获取所有需要的数据
            query = db.text(f"""
                SELECT
                    s.id, s.sector_code, s.sector_name, s.industry_level, s.description, s.is_active,
                    s.created_at, s.updated_at,
                    dq.quote_date as latest_quote_date, dq.close_price, dq.price_change, dq.price_change_pct,
                    ta.trend_direction, ta.trend_strength, ta.is_oscillating, ta.consecutive_up_days,
                    ta.is_new_high_5d, ta.is_new_high_20d
                FROM sectors s
                LEFT JOIN (
                    SELECT sector_id, quote_date, close_price, price_change, price_change_pct,
                           ROW_NUMBER() OVER (PARTITION BY sector_id ORDER BY quote_date DESC) as rn
                    FROM daily_quotes
                ) dq ON s.id = dq.sector_id AND dq.rn = 1
                LEFT JOIN (
                    SELECT sector_id, trend_direction, trend_strength, is_oscillating, consecutive_up_days,
                           is_new_high_5d, is_new_high_20d,
                           ROW_NUMBER() OVER (PARTITION BY sector_id ORDER BY analysis_date DESC) as rn
                    FROM technical_analysis
                ) ta ON s.id = ta.sector_id AND ta.rn = 1
                WHERE {where_clause}
                ORDER BY s.sector_code
                LIMIT :limit OFFSET :offset
            """)

            offset = (page - 1) * per_page
            result = db.session.execute(query, {"limit": per_page, "offset": offset}).fetchall()

            sectors_data = []
            for row in result:
                sector_info = {
                    'id': row[0],
                    'sector_code': row[1],
                    'sector_name': row[2],
                    'industry_level': row[3],
                    'description': row[4],
                    'is_active': bool(row[5]),
                    'created_at': row[6].isoformat() if row[6] else None,
                    'updated_at': row[7].isoformat() if row[7] else None
                }

                # 添加最新行情信息
                if row[8]:  # latest_quote_date
                    sector_info['latest_quote'] = {
                        'date': row[8].isoformat(),
                        'close_price': float(row[9]) if row[9] else None,
                        'price_change': float(row[10]) if row[10] else None,
                        'price_change_pct': float(row[11]) if row[11] else None
                    }

                # 添加最新分析信息
                if row[12]:  # trend_direction
                    sector_info['latest_analysis'] = {
                        'trend_direction': row[12],
                        'trend_strength': float(row[13]) if row[13] else None,
                        'is_oscillating': bool(row[14]) if row[14] is not None else None,
                        'consecutive_up_days': row[15] if row[15] else None,
                        'is_new_high_5d': bool(row[16]) if row[16] is not None else None,
                        'is_new_high_20d': bool(row[17]) if row[17] is not None else None
                    }

                sectors_data.append(sector_info)
        else:
            # 简化查询，只返回基本信息
            query = db.text(f"""
                SELECT id, sector_code, sector_name, industry_level, description, is_active,
                       created_at, updated_at
                FROM sectors
                WHERE {where_clause}
                ORDER BY sector_code
                LIMIT :limit OFFSET :offset
            """)

            offset = (page - 1) * per_page
            result = db.session.execute(query, {"limit": per_page, "offset": offset}).fetchall()

            sectors_data = []
            for row in result:
                sector_info = {
                    'id': row[0],
                    'sector_code': row[1],
                    'sector_name': row[2],
                    'industry_level': row[3],
                    'description': row[4],
                    'is_active': bool(row[5]),
                    'created_at': row[6].isoformat() if row[6] else None,
                    'updated_at': row[7].isoformat() if row[7] else None
                }
                sectors_data.append(sector_info)

        # 获取总数
        count_query = db.text(f"SELECT COUNT(*) FROM sectors WHERE {count_where_clause}")
        total_count = db.session.execute(count_query).scalar()
        total_pages = (total_count + per_page - 1) // per_page

        logger.info(f"成功处理 {len(sectors_data)} 个板块数据 (第{page}页，共{total_pages}页)")

        return jsonify({
            'success': True,
            'data': sectors_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取板块列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/all-realtime', methods=['GET'])
def get_all_sectors_realtime():
    """获取86个板块的缓存数据（页面加载时优先使用缓存，并自动计算技术指标）"""
    try:
        logger.info("页面加载：获取86个板块缓存数据并计算技术指标")

        # 使用全局数据服务实例，避免重复创建连接
        from services.data_service import data_service
        from services.analysis_service import analysis_service
        from services.technical_indicators_cache_service import indicators_cache_service

        # 优先从MySQL缓存获取基础数据
        cached_data = data_service.get_cached_sector_data()

        if cached_data is not None and not cached_data.empty:
            logger.info(f"从MySQL缓存获取到 {len(cached_data)} 个板块基础数据")

            # 批量获取所有板块的技术指标（优化关键点）
            all_indicators = {}
            if indicators_cache_service:
                # 批量查询技术指标缓存
                sector_codes = cached_data['板块代码'].tolist()
                logger.info(f"开始批量获取 {len(sector_codes)} 个板块的技术指标")

                start_time = time.time()
                all_indicators = indicators_cache_service.batch_get_indicators(sector_codes)
                elapsed = time.time() - start_time

                # 统计缓存命中情况
                hit_count = len([k for k, v in all_indicators.items() if v.get('trend_judgment') != '数据不足'])
                miss_count = len(sector_codes) - hit_count
                logger.info(f"批量获取技术指标完成，耗时: {elapsed:.3f}秒，查询 {len(sector_codes)} 个板块，命中 {hit_count} 个")

                # 智能技术指标计算策略
                if miss_count > 0:
                    logger.info(f"检测到 {miss_count} 个板块技术指标缓存未命中，开始智能计算")

                    calculated_count = 0
                    failed_count = 0

                    # 智能计算策略：平衡性能和覆盖率
                    if miss_count <= 20:
                        # 少量未命中：全部计算
                        logger.info(f"未命中数量较少({miss_count}个)，执行全量计算")
                        calculation_limit = miss_count
                    elif miss_count <= 40:
                        # 中等数量：计算大部分
                        calculation_limit = min(30, miss_count)
                        logger.info(f"未命中数量中等({miss_count}个)，计算前{calculation_limit}个板块")
                    else:
                        # 大量未命中：计算前25个，保证响应时间
                        calculation_limit = 25
                        logger.info(f"未命中数量较多({miss_count}个)，计算前{calculation_limit}个板块以保证响应时间")

                    # 执行计算
                    calculated_sectors = []
                    for sector_code in sector_codes:
                        if len(calculated_sectors) >= calculation_limit:
                            break

                        if all_indicators.get(sector_code, {}).get('trend_judgment') == '数据不足':
                            calculated_sectors.append(sector_code)

                            try:
                                # 使用get_or_calculate_indicators方法自动计算
                                calculated_indicators = indicators_cache_service.get_or_calculate_indicators(sector_code)
                                if calculated_indicators.get('trend_judgment') != '数据不足':
                                    all_indicators[sector_code] = calculated_indicators
                                    calculated_count += 1
                                    logger.debug(f"成功计算板块 {sector_code} 的技术指标")
                                else:
                                    failed_count += 1
                                    logger.debug(f"板块 {sector_code} 计算返回'数据不足'")
                            except Exception as e:
                                failed_count += 1
                                logger.warning(f"计算板块 {sector_code} 技术指标失败: {e}")

                    logger.info(f"智能计算完成: 尝试{len(calculated_sectors)}个, 成功{calculated_count}个, 失败{failed_count}个")

                    # 如果成功率太低，记录警告
                    if len(calculated_sectors) > 0:
                        success_rate = (calculated_count / len(calculated_sectors)) * 100
                        if success_rate < 50:
                            logger.warning(f"技术指标计算成功率较低: {success_rate:.1f}%，可能需要检查历史数据或计算逻辑")

            # 转换DataFrame为字典列表（优化版本：避免逐行计算）
            sectors_data = []
            for _, row in cached_data.iterrows():
                # 基础数据字段
                sector_dict = {
                    '排名': row.get('排名', 0),
                    '板块名称': row.get('板块名称', ''),
                    '板块代码': row.get('板块代码', ''),
                    '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                    '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                    '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                    '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                    '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                    '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                    '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                    '领涨股票': row.get('领涨股票', ''),
                    '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                    '领涨股票代码': row.get('领涨股票代码', ''),
                    '领涨股票价格': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else 0.0,
                    '数据更新时间': row.get('数据更新时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                }

                # 从批量获取的结果中添加技术指标
                sector_code = row.get('板块代码', '')
                if sector_code and sector_code in all_indicators:
                    indicators = all_indicators[sector_code]
                    sector_dict['趋势'] = indicators.get('trend_judgment', '数据不足')
                    sector_dict['震荡'] = indicators.get('oscillation_judgment', '数据不足')
                    sector_dict['连续上涨'] = indicators.get('consecutive_rise_judgment', '数据不足')
                    sector_dict['新高'] = indicators.get('new_high_judgment', '数据不足')
                else:
                    # 使用默认值
                    sector_dict['趋势'] = "数据不足"
                    sector_dict['震荡'] = "数据不足"
                    sector_dict['连续上涨'] = "数据不足"
                    sector_dict['新高'] = "数据不足"

                sectors_data.append(sector_dict)

            logger.info(f"成功从缓存获取 {len(sectors_data)} 个板块数据（含技术指标）")

            return jsonify({
                'success': True,
                'data': sectors_data,
                'total': len(sectors_data),
                'timestamp': datetime.now().isoformat(),
                'message': f'从缓存获取{len(sectors_data)}个板块数据（含技术指标）',
                'data_source': 'cache'
            })
        else:
            # 如果缓存为空，尝试获取实时数据作为后备
            logger.warning("缓存数据为空，尝试获取实时数据作为后备")
            realtime_data = data_service.get_enhanced_sector_realtime_data_with_indicators()

            if realtime_data.empty:
                logger.warning("获取的86个板块数据为空")
                return jsonify({
                    'success': False,
                    'error': '暂无数据',
                    'timestamp': datetime.now().isoformat()
                }), 404

            # 转换DataFrame为字典列表，确保所有字段都有默认值
            sectors_data = []
            for _, row in realtime_data.iterrows():
                sector_dict = {
                    '排名': row.get('排名', 0),
                    '板块名称': row.get('板块名称', ''),
                    '板块代码': row.get('板块代码', ''),
                    '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                    '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                    '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                    '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                    '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                    '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                    '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                    '领涨股票': row.get('领涨股票', ''),
                    '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                    '领涨股票代码': row.get('领涨股票代码', ''),
                    '领涨股票价格': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else 0.0,
                    '数据更新时间': row.get('数据更新时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    # 技术指标字段
                    '趋势': row.get('趋势', ''),
                    '震荡': row.get('震荡', ''),
                    '连续上涨': row.get('连续上涨', ''),
                    '新高': row.get('新高', ''),
                }
                sectors_data.append(sector_dict)

            logger.info(f"后备方案：成功获取 {len(sectors_data)} 个板块实时数据")

            return jsonify({
                'success': True,
                'data': sectors_data,
                'total': len(sectors_data),
                'timestamp': datetime.now().isoformat(),
                'message': f'后备方案：获取{len(sectors_data)}个板块实时数据',
                'data_source': 'api_fallback'
            })

    except Exception as e:
        logger.error(f"获取86个板块数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/calculate-indicators', methods=['POST'])
def calculate_sectors_indicators():
    """计算86个板块的技术指标（基于MySQL缓存的历史数据）"""
    try:
        logger.info("开始计算86个板块技术指标")

        # 使用全局服务实例，避免重复创建连接
        from services.data_service import data_service
        from services.analysis_service import TechnicalAnalysisService

        # 创建技术分析服务实例
        analysis_service = TechnicalAnalysisService()

        # 获取基础板块数据
        cached_data = data_service.get_cached_sector_data()

        if cached_data is None or cached_data.empty:
            logger.warning("没有找到缓存的板块数据")
            return jsonify({
                'success': False,
                'error': '没有找到板块数据，请先刷新数据',
                'timestamp': datetime.now().isoformat()
            }), 404

        logger.info(f"开始为 {len(cached_data)} 个板块计算技术指标")

        # 批量计算技术指标
        enhanced_data = []
        success_count = 0
        failed_count = 0

        for _, row in cached_data.iterrows():
            sector_code = row.get('板块代码', '')
            sector_name = row.get('板块名称', '')

            # 基础数据字典
            sector_dict = {
                '排名': row.get('排名', 0),
                '板块名称': sector_name,
                '板块代码': sector_code,
                '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                '领涨股票': row.get('领涨股票', ''),
                '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                '领涨股票代码': row.get('领涨股票代码', ''),
                '领涨股票价格': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else 0.0,
                '数据更新时间': row.get('数据更新时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            }

            # 计算技术指标
            try:
                if sector_code:
                    historical_data = data_service._get_sector_historical_for_indicators(sector_code, days=30)

                    if not historical_data.empty:
                        # 计算技术指标
                        indicators = analysis_service.calculate_sector_table_indicators(historical_data)
                        sector_dict['趋势'] = indicators['trend_judgment']
                        sector_dict['震荡'] = indicators['oscillation_judgment']
                        sector_dict['连续上涨'] = indicators['consecutive_rise_judgment']
                        sector_dict['新高'] = indicators['new_high_judgment']
                        success_count += 1
                        logger.debug(f"板块 {sector_name}({sector_code}) 技术指标计算完成")
                    else:
                        # 历史数据为空
                        sector_dict['趋势'] = "数据不足"
                        sector_dict['震荡'] = "数据不足"
                        sector_dict['连续上涨'] = "数据不足"
                        sector_dict['新高'] = "数据不足"
                        failed_count += 1
                        logger.debug(f"板块 {sector_name}({sector_code}) 历史数据为空")
                else:
                    # 板块代码为空
                    sector_dict['趋势'] = "代码缺失"
                    sector_dict['震荡'] = "代码缺失"
                    sector_dict['连续上涨'] = "代码缺失"
                    sector_dict['新高'] = "代码缺失"
                    failed_count += 1

            except Exception as e:
                logger.warning(f"板块 {sector_name}({sector_code}) 技术指标计算失败: {e}")
                sector_dict['趋势'] = "计算失败"
                sector_dict['震荡'] = "计算失败"
                sector_dict['连续上涨'] = "计算失败"
                sector_dict['新高'] = "计算失败"
                failed_count += 1

            enhanced_data.append(sector_dict)

        logger.info(f"技术指标计算完成：成功 {success_count} 个，失败 {failed_count} 个")

        return jsonify({
            'success': True,
            'data': enhanced_data,
            'total': len(enhanced_data),
            'timestamp': datetime.now().isoformat(),
            'message': f'技术指标计算完成：成功 {success_count} 个，失败 {failed_count} 个',
            'data_source': 'calculated_indicators',
            'statistics': {
                'total': len(enhanced_data),
                'success': success_count,
                'failed': failed_count,
                'success_rate': f"{success_count/len(enhanced_data)*100:.1f}%" if enhanced_data else "0%"
            }
        })

    except Exception as e:
        logger.error(f"计算86个板块技术指标失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/data/update-indicators', methods=['POST'])
def update_technical_indicators():
    """更新技术指标 - 同步处理版本（支持重试机制和300秒超时）"""
    import time
    from utils.retry_decorator import api_retry_with_progressive_delay

    start_time = time.time()

    try:
        logger.info("开始同步更新技术指标：使用增量更新策略")

        # 导入服务
        from services.incremental_update_service import IncrementalUpdateService
        from services.data_service import DataService
        from config import LocalConfig

        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        # 创建服务实例
        update_service = IncrementalUpdateService(mysql_config=mysql_config)
        data_service = DataService(mysql_config=mysql_config)

        # 获取当前缓存的86个板块数据
        cached_sectors = data_service.get_cached_sector_data()

        if cached_sectors.empty:
            return jsonify({
                'success': False,
                'error': '缓存中无板块数据，请先刷新数据',
                'timestamp': datetime.now().isoformat()
            }), 400

        # 统计信息
        total_sectors = len(cached_sectors)
        updated_sectors = 0
        failed_sectors = 0
        update_details = []

        logger.info(f"开始处理 {total_sectors} 个板块的技术指标更新")

        # 第一步：对每个板块执行增量更新检查（顺序处理，避免并发）
        for index, (_, sector) in enumerate(cached_sectors.iterrows()):
            sector_code = sector.get('板块代码', '')
            sector_name = sector.get('板块名称', '')

            if not sector_code:
                continue

            # 检查超时（300秒限制）
            elapsed_time = time.time() - start_time
            if elapsed_time > 290:  # 留10秒缓冲时间
                logger.warning(f"处理超时，已处理 {index + 1}/{total_sectors} 个板块")
                break

            logger.info(f"处理板块 {index + 1}/{total_sectors}: {sector_code}({sector_name})")

            # 使用重试机制处理单个板块
            sector_result = _process_single_sector_with_retry(
                update_service, sector_code, sector_name
            )

            if sector_result['status'] == 'success':
                updated_sectors += 1
            elif sector_result['status'] in ['failed', 'error']:
                failed_sectors += 1

            update_details.append(sector_result)

            # 顺序处理，避免API限流（每个板块间隔1秒）
            time.sleep(1)

        # 第二步：重新计算并更新技术指标到主数据表
        logger.info("开始重新计算技术指标并更新主数据表")
        indicators_updated = _recalculate_and_update_indicators(data_service)

        # 计算处理时间
        total_time = time.time() - start_time
        up_to_date_count = total_sectors - updated_sectors - failed_sectors

        logger.info(f"技术指标更新完成：总耗时 {total_time:.2f}秒，数据更新 {updated_sectors} 个板块，指标重算 {indicators_updated} 个板块")

        return jsonify({
            'success': True,
            'data': {
                'total_sectors': total_sectors,
                'updated_sectors': updated_sectors,
                'failed_sectors': failed_sectors,
                'up_to_date_sectors': up_to_date_count,
                'indicators_recalculated': indicators_updated,
                'update_details': update_details[:10],  # 只返回前10个详情，避免响应过大
                'strategy': 'sync_incremental_update_with_indicators',
                'processing_time_seconds': round(total_time, 2)
            },
            'message': f'技术指标同步更新完成：总计 {total_sectors} 个板块，数据更新 {updated_sectors} 个，指标重算 {indicators_updated} 个，失败 {failed_sectors} 个，已是最新 {up_to_date_count} 个，耗时 {total_time:.1f}秒',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)

        # 增强错误处理和日志记录
        logger.error(f"同步更新技术指标失败: {error_msg}，耗时 {total_time:.2f}秒", exc_info=True)

        # 根据错误类型提供更详细的错误信息
        if "timeout" in error_msg.lower():
            error_response = {
                'success': False,
                'error': '处理超时',
                'error_type': 'timeout',
                'message': '指标重算处理时间超过限制，请稍后重试或联系管理员',
                'processing_time_seconds': round(total_time, 2),
                'timestamp': datetime.now().isoformat()
            }
        elif "connection" in error_msg.lower():
            error_response = {
                'success': False,
                'error': '数据库连接错误',
                'error_type': 'connection',
                'message': '数据库连接失败，请检查网络连接或稍后重试',
                'processing_time_seconds': round(total_time, 2),
                'timestamp': datetime.now().isoformat()
            }
        else:
            error_response = {
                'success': False,
                'error': error_msg,
                'error_type': 'general',
                'message': f'指标重算过程中发生错误: {error_msg}',
                'processing_time_seconds': round(total_time, 2),
                'timestamp': datetime.now().isoformat()
            }

        return jsonify(error_response), 500


def _process_single_sector_with_retry(update_service, sector_code: str, sector_name: str) -> dict:
    """
    使用重试机制处理单个板块的技术指标更新

    Args:
        update_service: 增量更新服务实例
        sector_code: 板块代码
        sector_name: 板块名称

    Returns:
        处理结果字典
    """
    from utils.retry_decorator import api_retry_with_progressive_delay

    @api_retry_with_progressive_delay(
        max_retries=5,  # 最多5次重试
        delay_sequence=[5, 10, 20, 30, 30],  # 5s->10s->20s->30s->30s
        exceptions=(Exception,),
        on_retry=lambda attempt, error, delay: logger.warning(
            f"板块 {sector_code}({sector_name}) 第 {attempt} 次重试，错误: {error}"
        )
    )
    def _update_sector_data():
        """执行单个板块的数据更新"""
        # 检查缺失的交易日
        missing_dates = update_service.get_missing_trading_dates(sector_code)

        # 检查最后交易日数据新鲜度
        freshness_check = update_service.check_last_trading_day_freshness(sector_code)

        # 如果有缺失数据或需要更新最后交易日，执行更新
        needs_update = len(missing_dates) > 0 or freshness_check.get('need_update', False)

        if needs_update:
            # 执行增量更新
            all_dates_to_update = missing_dates.copy()
            if freshness_check.get('need_update', False):
                last_date = freshness_check.get('last_trading_date')
                if last_date and last_date not in all_dates_to_update:
                    all_dates_to_update.append(last_date)

            update_result = update_service.perform_incremental_update(sector_code, all_dates_to_update)

            if update_result.get('success', False):
                return {
                    'sector_code': sector_code,
                    'sector_name': sector_name,
                    'updated_dates': len(all_dates_to_update),
                    'status': 'success'
                }
            else:
                raise Exception(update_result.get('error', '更新失败'))
        else:
            # 数据已是最新，无需更新
            return {
                'sector_code': sector_code,
                'sector_name': sector_name,
                'status': 'up_to_date'
            }

    try:
        return _update_sector_data()
    except Exception as e:
        logger.error(f"板块 {sector_code}({sector_name}) 更新失败: {e}")
        return {
            'sector_code': sector_code,
            'sector_name': sector_name,
            'error': str(e),
            'status': 'failed'
        }


def _recalculate_and_update_indicators(data_service) -> int:
    """
    重新计算技术指标并更新到主数据表（选择性更新策略：只更新涨跌幅前10的板块）

    Args:
        data_service: 数据服务实例

    Returns:
        成功更新的板块数量
    """
    try:
        from services.analysis_service import TechnicalAnalysisService

        # 创建技术分析服务
        analysis_service = TechnicalAnalysisService()

        # 🔧 修复：一次性获取板块数据，避免循环中重复查询
        sectors_data = data_service.get_cached_sector_data()

        if sectors_data.empty:
            logger.warning("没有找到板块数据，无法重新计算技术指标")
            return 0

        # 🚀 性能优化：选择性更新策略 - 只对涨跌幅前10的板块重新计算技术指标
        logger.info(f"🚀 开始选择性技术指标更新：从 {len(sectors_data)} 个板块中筛选涨跌幅前10进行重新计算")

        # 按涨跌幅降序排序，获取前10个板块
        sectors_sorted = sectors_data.sort_values('涨跌幅', ascending=False)
        top_10_sectors = sectors_sorted.head(10)

        logger.info(f"📊 涨跌幅前10板块筛选完成:")
        for i, (_, sector) in enumerate(top_10_sectors.iterrows(), 1):
            logger.info(f"   {i}. {sector.get('板块名称', 'N/A')} ({sector.get('板块代码', 'N/A')}) 涨跌幅: {sector.get('涨跌幅', 0):.2f}%")

        updated_count = 0
        cached_count = 0

        # 🔧 修复：使用共享的缓存数据引用，避免每次循环都查询数据库
        shared_cached_data = sectors_data.copy()  # 创建副本用于更新

        # 🚀 选择性更新：只对涨跌幅前10的板块重新计算技术指标
        top_10_codes = set(top_10_sectors['板块代码'].tolist())

        for index, (_, sector) in enumerate(sectors_data.iterrows()):
            sector_code = sector.get('板块代码', '')
            sector_name = sector.get('板块名称', '')
            sector_change = sector.get('涨跌幅', 0)

            if not sector_code:
                continue

            # 判断是否为涨跌幅前10的板块
            is_top_10 = sector_code in top_10_codes

            try:
                if is_top_10:
                    # 🔥 涨跌幅前10：重新获取历史数据并计算技术指标
                    logger.info(f"🔥 重新计算板块 {sector_code}({sector_name}) 技术指标，涨跌幅: {sector_change:.2f}%")

                    # 智能获取历史数据：优先缓存，缓存未命中时调用API
                    historical_data = _get_historical_data_with_fallback(data_service, sector_code, days=60)

                    if not historical_data.empty:
                        # 计算技术指标
                        indicators = analysis_service.calculate_sector_table_indicators(historical_data)

                        # 🔧 修复：传入共享缓存数据，避免重复查询
                        shared_cached_data = _update_sector_indicators_in_cache(
                            data_service, sector_code, indicators, shared_cached_data
                        )

                        # 🏷️ 添加"新"标记，表示技术指标已更新
                        mask = shared_cached_data['板块代码'] == sector_code
                        if mask.any():
                            # 在板块名称后添加"新"标记
                            original_name = shared_cached_data.loc[mask, '板块名称'].iloc[0]
                            if not original_name.endswith('新'):
                                shared_cached_data.loc[mask, '板块名称'] = original_name + '新'

                        updated_count += 1
                        logger.info(f"✅ 板块 {sector_code}({sector_name}) 技术指标重新计算完成")
                    else:
                        # 历史数据不足，使用默认值
                        default_indicators = {
                            'trend_judgment': '数据不足',
                            'oscillation_judgment': '数据不足',
                            'consecutive_rise_judgment': '数据不足',
                            'new_high_judgment': '数据不足'
                        }

                        shared_cached_data = _update_sector_indicators_in_cache(
                            data_service, sector_code, default_indicators, shared_cached_data
                        )

                        logger.warning(f"⚠️  板块 {sector_code}({sector_name}) 历史数据不足，使用默认值")
                else:
                    # 📋 其余板块：保持现有技术指标不变
                    cached_count += 1
                    logger.debug(f"📋 板块 {sector_code}({sector_name}) 使用缓存技术指标，涨跌幅: {sector_change:.2f}%")

            except Exception as e:
                logger.error(f"❌ 板块 {sector_code}({sector_name}) 技术指标处理失败: {e}")
                if is_top_10:
                    # 如果是前10板块处理失败，记录为跳过
                    pass

        # 🔧 修复：循环结束后一次性更新缓存，而不是每次循环都更新
        if shared_cached_data is not None and not shared_cached_data.empty:
            try:
                data_service._set_cache('enhanced_sector_realtime_data', shared_cached_data)
                logger.info(f"✅ 批量更新缓存成功，共更新 {len(shared_cached_data)} 个板块数据")
            except Exception as e:
                logger.error(f"批量更新缓存失败: {e}")

        # 📊 选择性更新统计报告
        logger.info(f"🎉 选择性技术指标更新完成:")
        logger.info(f"   📈 重新计算板块: {updated_count} 个 (涨跌幅前10)")
        logger.info(f"   📋 使用缓存板块: {cached_count} 个 (其余板块)")
        logger.info(f"   📊 总计板块: {updated_count + cached_count} 个")
        logger.info(f"   ⚡ 性能提升: 减少了 {cached_count} 个板块的重复计算")

        return updated_count

    except Exception as e:
        logger.error(f"重新计算技术指标失败: {e}")
        return 0


def _get_cached_historical_data_only(data_service, sector_code: str, days: int = 60) -> pd.DataFrame:
    """
    仅从MySQL缓存获取历史数据，不进行API调用（避免超时）

    Args:
        data_service: 数据服务实例
        sector_code: 板块代码
        days: 历史天数

    Returns:
        历史数据DataFrame
    """
    try:
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

        # 只从MySQL缓存获取历史数据
        if data_service.cache_service:
            cached_data = data_service.cache_service.cache_manager.get_cache(
                'historical_data_cache',
                'sector_historical_data',
                sector_code=sector_code,
                start_date=start_date,
                end_date=end_date
            )
            if cached_data is not None:
                logger.debug(f"从MySQL缓存获取板块 {sector_code} 历史数据，共 {len(cached_data)} 条记录")
                return cached_data

        # 缓存未命中，返回空DataFrame（不进行API调用）
        logger.debug(f"板块 {sector_code} MySQL缓存未命中，跳过API调用避免超时")
        return pd.DataFrame()

    except Exception as e:
        logger.error(f"获取板块 {sector_code} 缓存历史数据失败: {e}")
        return pd.DataFrame()

def _get_historical_data_with_fallback(data_service, sector_code: str, days: int = 60) -> pd.DataFrame:
    """
    智能获取历史数据：优先缓存，缓存未命中时调用API
    用于智能更新后的技术指标计算

    Args:
        data_service: 数据服务实例
        sector_code: 板块代码
        days: 历史天数

    Returns:
        历史数据DataFrame
    """
    try:
        # 首先尝试从缓存获取
        cached_data = _get_cached_historical_data_only(data_service, sector_code, days)
        if not cached_data.empty:
            return cached_data

        # 缓存未命中，调用API获取（智能更新时允许）
        logger.info(f"板块 {sector_code} 缓存未命中，调用API获取历史数据")
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

        api_data = data_service.get_sector_historical_data(
            sector_code=sector_code,
            start_date=start_date,
            end_date=end_date
        )

        if not api_data.empty:
            logger.info(f"API获取板块 {sector_code} 历史数据成功，共 {len(api_data)} 条记录")
            return api_data
        else:
            logger.warning(f"API获取板块 {sector_code} 历史数据为空")
            return pd.DataFrame()

    except Exception as e:
        logger.error(f"获取板块 {sector_code} 历史数据失败: {e}")
        return pd.DataFrame()


def _update_sector_indicators_in_cache(data_service, sector_code: str, indicators: dict, cached_data=None):
    """
    更新缓存中的板块技术指标（优化版本：避免重复查询）

    Args:
        data_service: 数据服务实例
        sector_code: 板块代码
        indicators: 技术指标字典
        cached_data: 预先获取的缓存数据（避免重复查询）
    """
    try:
        # 🔧 修复：如果没有传入缓存数据，才进行查询（避免循环中重复查询）
        if cached_data is None:
            if hasattr(data_service, 'cache_service') and data_service.cache_service:
                cached_data = data_service.get_cached_sector_data()
            else:
                logger.warning(f"缓存服务不可用，无法更新板块 {sector_code} 技术指标")
                return None

        if cached_data is None or cached_data.empty:
            logger.warning(f"缓存数据为空，无法更新板块 {sector_code} 技术指标")
            return None

        # 更新对应板块的技术指标
        mask = cached_data['板块代码'] == sector_code
        if mask.any():
            cached_data.loc[mask, '趋势'] = indicators['trend_judgment']
            cached_data.loc[mask, '震荡'] = indicators['oscillation_judgment']
            cached_data.loc[mask, '连续上涨'] = indicators['consecutive_rise_judgment']
            cached_data.loc[mask, '新高'] = indicators['new_high_judgment']

            logger.debug(f"板块 {sector_code} 技术指标已更新到内存缓存")
            return cached_data  # 返回更新后的数据供后续使用
        else:
            logger.warning(f"在缓存数据中未找到板块 {sector_code}")
            return cached_data

    except Exception as e:
        logger.error(f"更新板块 {sector_code} 技术指标到缓存失败: {e}")
        return cached_data


@api_bp.route('/data/check-freshness', methods=['GET'])
def check_data_freshness():
    """检查数据新鲜度"""
    try:
        logger.info("检查数据新鲜度")

        # 导入服务
        from services.data_freshness_service import DataFreshnessService
        from services.data_service import DataService
        from config import LocalConfig

        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        # 创建服务实例
        freshness_service = DataFreshnessService(mysql_config=mysql_config)
        data_service = DataService(mysql_config=mysql_config)

        # 检查申万31行业数据新鲜度
        shenwan31_data = data_service.get_cached_shenwan31_data()
        shenwan31_freshness = freshness_service.check_sector_data_freshness(shenwan31_data)
        shenwan31_recommendation = freshness_service.get_update_recommendation(shenwan31_freshness)

        # 检查86个板块数据新鲜度
        all_sectors_data = data_service.get_cached_sector_data()
        all_sectors_freshness = freshness_service.check_sector_data_freshness(all_sectors_data)
        all_sectors_recommendation = freshness_service.get_update_recommendation(all_sectors_freshness)

        return jsonify({
            'success': True,
            'data': {
                'shenwan31': {
                    'freshness': shenwan31_freshness,
                    'recommendation': shenwan31_recommendation,
                    'data_count': len(shenwan31_data) if not shenwan31_data.empty else 0
                },
                'all_sectors': {
                    'freshness': all_sectors_freshness,
                    'recommendation': all_sectors_recommendation,
                    'data_count': len(all_sectors_data) if not all_sectors_data.empty else 0
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"检查数据新鲜度失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/shenwan31-optimized', methods=['GET'])
def get_shenwan31_optimized():
    """获取申万31个行业缓存数据 - 启动性能优化版本（严格仅使用缓存）"""
    try:
        logger.info("页面启动：获取申万31个行业缓存数据（仅缓存模式）")

        # 导入数据服务
        from services.data_service import DataService
        from config import LocalConfig

        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        # 创建数据服务（自动启用MySQL缓存）
        data_service = DataService(mysql_config=mysql_config)

        # 严格仅从MySQL缓存获取申万31行业数据，绝不触发API调用
        cached_data = data_service.get_cached_shenwan31_data()

        if cached_data is not None and not cached_data.empty:
            # 转换DataFrame为前端期望的格式
            sectors_data = []
            for _, row in cached_data.iterrows():
                sector_info = {
                    'id': None,  # 申万行业没有数据库ID
                    'sector_code': row.get('板块代码', ''),
                    'sector_name': row.get('板块名称', ''),
                    'industry_level': '一级',
                    'description': f"申万一级行业 - {row.get('板块名称', '')}",
                    'is_active': True,
                    'created_at': None,
                    'updated_at': None,
                    'latest_quote': {
                        'date': datetime.now().date().isoformat(),
                        'close_price': float(row.get('最新价', 0)) if row.get('最新价') else None,
                        'price_change': float(row.get('涨跌额', 0)) if row.get('涨跌额') else None,
                        'price_change_pct': float(row.get('涨跌幅', 0)) if row.get('涨跌幅') else None
                    },
                    'latest_analysis': {
                        'trend_direction': '上涨' if float(row.get('涨跌幅', 0)) > 0 else ('下跌' if float(row.get('涨跌幅', 0)) < 0 else '震荡'),
                        'trend_strength': abs(float(row.get('涨跌幅', 0))) if row.get('涨跌幅') else 0,
                        'is_oscillating': abs(float(row.get('涨跌幅', 0))) < 1 if row.get('涨跌幅') else True,
                        'consecutive_up_days': None,
                        'is_new_high_5d': None,
                        'is_new_high_20d': None
                    }
                }
                sectors_data.append(sector_info)

            logger.info(f"成功从缓存获取 {len(sectors_data)} 个申万行业数据")

            return jsonify({
                'success': True,
                'data': sectors_data,
                'total': len(sectors_data),
                'timestamp': datetime.now().isoformat(),
                'message': f'从缓存获取{len(sectors_data)}个申万行业数据',
                'data_source': 'cache'
            })
        else:
            # 启动性能优化：缓存为空时不调用API，返回空数据提示用户手动刷新
            logger.warning("页面启动：申万31行业缓存数据为空，建议用户手动刷新")
            return jsonify({
                'success': True,
                'data': [],
                'total': 0,
                'timestamp': datetime.now().isoformat(),
                'message': '缓存数据为空，请点击"刷新数据"按钮获取最新数据',
                'data_source': 'cache_empty',
                'need_refresh': True
            })

    except Exception as e:
        logger.error(f"获取申万31个行业数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/refresh', methods=['POST'])
def refresh_sectors_data():
    """手动刷新板块数据（根据交易时间判断是否调用API）"""
    try:
        # 获取请求参数
        data_type = request.json.get('data_type', 'all') if request.json else 'all'  # 'all' 或 'shenwan31'
        force_refresh = request.json.get('force_refresh', False) if request.json else False

        logger.info(f"手动刷新数据请求：数据类型={data_type}, 强制刷新={force_refresh}")

        # 导入数据服务和交易日历
        from services.data_service import DataService
        from config import LocalConfig
        from utils.trading_calendar import trading_calendar

        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        # 创建数据服务（自动启用MySQL缓存）
        data_service = DataService(mysql_config=mysql_config)

        # 判断当前是否为交易时间
        is_trading_time = trading_calendar.is_trading_time()
        logger.info(f"当前交易时间状态: {is_trading_time}")

        # 根据交易时间和强制刷新参数决定数据获取策略
        if is_trading_time or force_refresh:
            # 交易时间内或强制刷新：尝试从API获取最新数据
            logger.info("交易时间内或强制刷新，尝试从API获取最新数据")

            if data_type == 'shenwan31':
                # 刷新申万31行业数据（支持强制刷新）
                realtime_data = data_service.get_shenwan_31_realtime_data_optimized(force_refresh=force_refresh)
                data_description = "申万31行业"
            else:
                # 刷新86个板块数据（支持强制刷新）并计算技术指标
                realtime_data = data_service.get_enhanced_sector_realtime_data_with_indicators()
                data_description = "86个板块（含技术指标）"

            if realtime_data.empty:
                # API获取失败，尝试从缓存获取
                logger.warning(f"API获取{data_description}数据失败，尝试从缓存获取")

                if data_type == 'shenwan31':
                    cached_data = data_service.get_cached_shenwan31_data()
                else:
                    cached_data = data_service.get_cached_sector_data()

                if cached_data is not None and not cached_data.empty:
                    sectors_data = cached_data.to_dict('records')
                    return jsonify({
                        'success': True,
                        'data': sectors_data,
                        'total': len(sectors_data),
                        'timestamp': datetime.now().isoformat(),
                        'message': f'API失败，从缓存获取{len(sectors_data)}个{data_description}数据',
                        'data_source': 'cache_fallback',
                        'trading_time': is_trading_time
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': f'API和缓存都无法获取{data_description}数据',
                        'timestamp': datetime.now().isoformat(),
                        'trading_time': is_trading_time
                    }), 404
            else:
                # API获取成功，转换数据格式确保字段完整
                sectors_data = []
                for _, row in realtime_data.iterrows():
                    sector_dict = {
                        '排名': row.get('排名', 0),
                        '板块名称': row.get('板块名称', ''),
                        '板块代码': row.get('板块代码', ''),
                        '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                        '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                        '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                        '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                        '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                        '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                        '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                        '领涨股票': row.get('领涨股票', ''),
                        '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                        '领涨股票代码': row.get('领涨股票代码', ''),
                        '领涨股票价格': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else 0.0,
                        '数据更新时间': row.get('数据更新时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                        # 技术指标字段
                        '趋势': row.get('趋势', ''),
                        '震荡': row.get('震荡', ''),
                        '连续上涨': row.get('连续上涨', ''),
                        '新高': row.get('新高', ''),
                    }
                    sectors_data.append(sector_dict)

                logger.info(f"API成功获取 {len(sectors_data)} 个{data_description}数据")

                return jsonify({
                    'success': True,
                    'data': sectors_data,
                    'total': len(sectors_data),
                    'timestamp': datetime.now().isoformat(),
                    'message': f'API成功获取{len(sectors_data)}个{data_description}最新数据',
                    'data_source': 'api',
                    'trading_time': is_trading_time
                })
        else:
            # 非交易时间：直接从缓存获取数据
            logger.info("非交易时间，直接从缓存获取数据")

            if data_type == 'shenwan31':
                cached_data = data_service.get_cached_shenwan31_data()
                data_description = "申万31行业"
            else:
                cached_data = data_service.get_cached_sector_data()
                data_description = "86个板块"

            if cached_data is not None and not cached_data.empty:
                # 转换缓存数据格式，确保字段完整
                sectors_data = []
                for _, row in cached_data.iterrows():
                    sector_dict = {
                        '排名': row.get('排名', 0),
                        '板块名称': row.get('板块名称', ''),
                        '板块代码': row.get('板块代码', ''),
                        '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                        '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                        '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                        '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                        '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                        '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                        '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                        '领涨股票': row.get('领涨股票', ''),
                        '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                        '领涨股票代码': row.get('领涨股票代码', ''),
                        '领涨股票价格': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else 0.0,
                        '数据更新时间': row.get('数据更新时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                        # 技术指标字段
                        '趋势': row.get('趋势', ''),
                        '震荡': row.get('震荡', ''),
                        '连续上涨': row.get('连续上涨', ''),
                        '新高': row.get('新高', ''),
                    }
                    sectors_data.append(sector_dict)

                logger.info(f"非交易时间，从缓存获取 {len(sectors_data)} 个{data_description}数据")

                return jsonify({
                    'success': True,
                    'data': sectors_data,
                    'total': len(sectors_data),
                    'timestamp': datetime.now().isoformat(),
                    'message': f'非交易时间，从缓存获取{len(sectors_data)}个{data_description}数据',
                    'data_source': 'cache',
                    'trading_time': is_trading_time
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f'缓存中无{data_description}数据',
                    'timestamp': datetime.now().isoformat(),
                    'trading_time': is_trading_time
                }), 404

    except Exception as e:
        logger.error(f"刷新板块数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/<sector_code>', methods=['GET'])
def get_sector_detail(sector_code: str):
    """获取单个板块详情"""
    try:
        # 申万代码到东财代码的映射
        sw_to_bk_mapping = {
            '801010': 'BK0433',  # 农林牧渔 -> 农牧饲渔
            '801020': 'BK0437',  # 采掘 -> 煤炭行业
            '801030': 'BK1019',  # 化工 -> 化学原料
            '801040': 'BK0479',  # 钢铁 -> 钢铁行业
            '801050': 'BK0478',  # 有色金属 -> 有色金属
            '801080': 'BK1036',  # 电子 -> 半导体
            '801110': 'BK0456',  # 家用电器 -> 家电行业
            '801120': 'BK0438',  # 食品饮料 -> 食品饮料
            '801130': 'BK0436',  # 纺织服装 -> 纺织服装
            '801140': 'BK0440',  # 轻工制造 -> 家用轻工
            '801150': 'BK0465',  # 医药生物 -> 化学制药
            '801160': 'BK0427',  # 公用事业 -> 公用事业
            '801170': 'BK0421',  # 交通运输 -> 铁路公路
            '801180': 'BK0451',  # 房地产 -> 房地产开发
            '801200': 'BK0482',  # 商业贸易 -> 商业百货
            '801210': 'BK0485',  # 休闲服务 -> 旅游酒店
            '801230': 'BK0539',  # 综合 -> 综合行业
            '801710': 'BK0424',  # 建筑材料 -> 水泥建材
            '801720': 'BK0429',  # 建筑装饰 -> 建筑建材
            '801730': 'BK0531',  # 电气设备 -> 电气设备
            '801740': 'BK0534',  # 国防军工 -> 国防军工
            '801750': 'BK0477',  # 计算机 -> 计算机应用
            '801760': 'BK0484',  # 传媒 -> 文化传媒
            '801770': 'BK0447',  # 通信 -> 通信行业
            '801780': 'BK0475',  # 银行 -> 银行
            '801790': 'BK0474',  # 非银金融 -> 保险
            '801880': 'BK0481',  # 汽车 -> 汽车行业
            '801890': 'BK0432',  # 机械设备 -> 机械行业
            '801950': 'BK0437',  # 煤炭 -> 煤炭行业
            '801960': 'BK0444',  # 石油石化 -> 石油行业
            '801970': 'BK0537'   # 环保 -> 环保工程
        }

        # 处理不同的板块代码格式
        actual_sector_code = sector_code

        # 如果是申万代码，转换为BK代码
        if sector_code.startswith('801') and sector_code in sw_to_bk_mapping:
            actual_sector_code = sw_to_bk_mapping[sector_code]
            logger.info(f"申万代码 {sector_code} 映射到东财代码 {actual_sector_code}")

        # 直接查找板块（支持BK代码和申万代码）
        sector = Sector.get_by_code(actual_sector_code)

        # 如果没找到且是BK代码，尝试直接使用原始代码
        if not sector and sector_code.startswith('BK'):
            sector = Sector.get_by_code(sector_code)
            actual_sector_code = sector_code
            logger.info(f"直接使用东财代码 {sector_code}")
        if not sector:
            logger.warning(f"板块详情查询失败: 原始代码={sector_code}, 映射后代码={actual_sector_code}")
            # 提供更详细的错误信息
            error_msg = f'未找到板块: {sector_code}'
            if sector_code != actual_sector_code:
                error_msg += f' (已尝试映射到: {actual_sector_code})'

            return jsonify({
                'success': False,
                'error': error_msg,
                'debug_info': {
                    'original_code': sector_code,
                    'mapped_code': actual_sector_code,
                    'is_shenwan_code': sector_code.startswith('801')
                },
                'timestamp': datetime.now().isoformat()
            }), 404
        
        # 获取历史行情数据
        days = request.args.get('days', 30, type=int)
        analysis_days = request.args.get('analysis_days', 7, type=int)
        start_date = datetime.now() - timedelta(days=days)

        logger.info(f"获取板块详情: {sector.sector_name} ({sector.sector_code}), 查询{days}天历史数据")

        quotes = DailyQuote.get_sector_quotes(sector.id, start_date=start_date.date())

        # 获取技术分析数据 - 优先使用数据库，如果为空则实时计算
        try:
            # 首先尝试从数据库获取技术分析数据
            analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                            .filter(TechnicalAnalysis.analysis_date >= start_date.date())\
                                            .order_by(TechnicalAnalysis.analysis_date.desc())\
                                            .all()
            
            # 如果数据库中没有技术分析数据，则实时计算
            if not analyses and quotes:
                logger.info(f"数据库中无技术分析数据，为板块 {sector.sector_name} 实时计算技术指标")
                
                # 使用与主页面相同的服务进行实时计算
                from services.data_service import data_service
                from services.analysis_service import analysis_service
                import pandas as pd
                
                # 获取历史数据用于计算
                historical_data = data_service._get_sector_historical_for_indicators(actual_sector_code, days=days)
                
                if not historical_data.empty:
                    # 计算技术指标
                    indicators = analysis_service.calculate_sector_table_indicators(historical_data)
                    
                    # 创建虚拟的技术分析数据对象（用于前端显示）
                    virtual_analyses = []
                    for i, (_, row) in enumerate(historical_data.iterrows()):
                        if i < min(30, len(historical_data)):  # 只创建最近30天的数据
                            virtual_analysis = {
                                'id': f'virtual_{i}',
                                'analysis_date': row.get('date', datetime.now().strftime('%Y-%m-%d')),
                                'ma5': float(row.get('ma5', 0)) if pd.notna(row.get('ma5')) else 0.0,
                                'ma10': float(row.get('ma10', 0)) if pd.notna(row.get('ma10')) else 0.0,
                                'ma20': float(row.get('ma20', 0)) if pd.notna(row.get('ma20')) else 0.0,
                                'ma60': float(row.get('ma60', 0)) if pd.notna(row.get('ma60')) else 0.0,
                                'trend_type': indicators.get('trend_judgment', '数据不足'),
                                'volatility_level': indicators.get('oscillation_judgment', '数据不足')
                            }
                            virtual_analyses.append(virtual_analysis)
                    
                    # 将虚拟分析数据转换为字典格式
                    analyses_dict = virtual_analyses
                    logger.info(f"为板块 {sector.sector_name} 生成了 {len(virtual_analyses)} 条虚拟技术分析数据")
                else:
                    analyses_dict = []
                    logger.warning(f"板块 {sector.sector_name} 无法获取历史数据进行技术指标计算")
            else:
                # 使用数据库中的数据
                analyses_dict = [analysis.to_dict() for analysis in analyses]
                logger.info(f"从数据库获取到板块 {sector.sector_name} 的 {len(analyses)} 条技术分析数据")

        except Exception as e:
            logger.error(f"获取/计算技术分析数据失败: {e}")
            analyses_dict = []
            analyses = []

        logger.info(f"板块详情查询结果: 历史数据{len(quotes)}条, 分析数据{len(analyses_dict)}条")

        sector_data = sector.to_dict()
        sector_data['quotes'] = [quote.to_dict() for quote in quotes]
        sector_data['analyses'] = analyses_dict

        return jsonify({
            'success': True,
            'data': sector_data,
            'debug_info': {
                'original_code': sector_code,
                'mapped_code': actual_sector_code,
                'quotes_count': len(quotes),
                'analyses_count': len(analyses),
                'query_days': days,
                'analysis_days': analysis_days
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取板块详情失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/data/update-missing', methods=['POST'])
def update_missing_analysis():
    """快速更新缺少技术分析数据的板块"""
    try:
        result = database_service.update_missing_analysis()
        return jsonify({
            'success': True,
            'data': result,
            'message': f'快速更新完成，成功 {result["success_count"]}/{result["total_sectors"]} 个板块，耗时 {result.get("elapsed_seconds", 0)} 秒',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"快速更新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/<sector_code>/update', methods=['POST'])
def update_single_sector(sector_code: str):
    """更新单个板块的数据和技术指标"""
    try:
        logger.info(f"开始更新单个板块: {sector_code}")

        # 导入必要的服务和模型
        from services.data_service import data_service
        from services.database_service import database_service
        from services.technical_indicators_cache_service import indicators_cache_service
        from utils.trading_calendar import trading_calendar
        from models import Sector, DailyQuote, TechnicalAnalysis
        from database import db
        import pandas as pd

        update_results = {
            'sector_code': sector_code,
            'historical_data_updated': False,
            'realtime_data_updated': False,
            'indicators_updated': False,
            'latest_date': None,
            'data_count': 0,
            'update_time': datetime.now().isoformat()
        }

        # 1. 强制刷新该板块的历史数据（忽略缓存）并存储到数据库
        logger.info(f"强制更新板块 {sector_code} 的历史数据")

        try:
            # 获取最近30天的历史数据，强制从API获取
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')

            # 清除相关缓存，确保获取最新数据
            if data_service.cache_service:
                # 强制清除历史数据缓存
                try:
                    # 清除特定板块的历史数据缓存
                    _force_clear_sector_cache(data_service.cache_service.cache_manager, 'historical_data_cache', sector_code)
                    logger.info(f"已清除板块 {sector_code} 的历史数据缓存")
                except Exception as cache_e:
                    logger.warning(f"清除缓存失败: {cache_e}")

            # 强制从API获取最新历史数据
            historical_data = data_service.get_sector_historical_data(
                sector_code=sector_code,
                start_date=start_date,
                end_date=end_date
            )

            if not historical_data.empty:
                update_results['historical_data_updated'] = True
                update_results['data_count'] = len(historical_data)
                update_results['latest_date'] = str(historical_data.iloc[-1].get('日期', ''))
                logger.info(f"成功获取板块 {sector_code} 历史数据，共 {len(historical_data)} 条，最新日期: {update_results['latest_date']}")

                # 🔧 修复：检查数据是否为最新交易日（包括今天如果今天是交易日）
                latest_trading_date = trading_calendar.get_latest_trading_day()
                # 🔧 修复：正确处理日期格式，提取日期部分
                latest_date_str = update_results['latest_date']
                if 'T' in latest_date_str:
                    # 如果是ISO格式，提取日期部分
                    data_latest_date = latest_date_str.split('T')[0].replace('-', '')
                else:
                    # 如果是简单日期格式
                    data_latest_date = latest_date_str.replace('-', '')
                latest_trading_date_str = latest_trading_date.strftime('%Y%m%d')

                # 🔧 修复：先存储已获取的历史数据到数据库
                try:
                    database_update_success = database_service.update_sector_quotes(sector_code, days=30)
                    if database_update_success:
                        logger.info(f"成功将板块 {sector_code} 历史数据存储到数据库")
                        update_results['database_stored'] = True
                    else:
                        logger.warning(f"板块 {sector_code} 历史数据存储到数据库失败")
                        update_results['database_stored'] = False
                except Exception as db_e:
                    logger.error(f"存储板块 {sector_code} 历史数据到数据库失败: {db_e}")
                    update_results['database_stored'] = False

                # 🔧 修复：检查数据是否为最新交易日，如果不是则获取今天的数据
                logger.info(f"🔍 日期比较调试: data_latest_date={data_latest_date}, latest_trading_date_str={latest_trading_date_str}")
                logger.info(f"🔍 比较结果: {data_latest_date} < {latest_trading_date_str} = {data_latest_date < latest_trading_date_str}")

                if data_latest_date < latest_trading_date_str:
                    logger.warning(f"板块 {sector_code} 数据日期 {data_latest_date} 早于最新交易日 {latest_trading_date_str}")
                    logger.info(f"尝试获取板块 {sector_code} 今天({latest_trading_date_str})的数据")
                else:
                    logger.info(f"板块 {sector_code} 数据已是最新交易日，无需获取今天数据")
                    try:
                        # 获取今天的数据
                        today_data = data_service.get_sector_historical_data(
                            sector_code=sector_code,
                            start_date=latest_trading_date_str,
                            end_date=latest_trading_date_str
                        )
                        if not today_data.empty:
                            logger.info(f"成功获取板块 {sector_code} 今天的数据")

                            # 🔧 关键修复：直接将今天的数据存储到数据库
                            try:
                                # 手动存储今天的数据到数据库
                                sector = Sector.get_by_code(sector_code)
                                if sector:
                                    for _, row in today_data.iterrows():
                                        quote_date = pd.to_datetime(row['日期']).date()

                                        # 检查是否已存在今天的数据
                                        existing_quote = DailyQuote.query.filter_by(
                                            sector_id=sector.id,
                                            quote_date=quote_date
                                        ).first()

                                        if existing_quote:
                                            # 更新现有记录
                                            existing_quote.open_price = float(row['开盘'])
                                            existing_quote.high_price = float(row['最高'])
                                            existing_quote.low_price = float(row['最低'])
                                            existing_quote.close_price = float(row['收盘'])
                                            existing_quote.volume = int(row['成交量']) if '成交量' in row else None
                                            existing_quote.turnover = float(row['成交额']) if '成交额' in row else None
                                            existing_quote.price_change = float(row['涨跌额']) if '涨跌额' in row else None
                                            existing_quote.price_change_pct = float(row['涨跌幅']) if '涨跌幅' in row else None
                                            existing_quote.updated_at = datetime.now()
                                            logger.info(f"更新了板块 {sector_code} 日期 {quote_date} 的行情数据")
                                        else:
                                            # 创建新记录
                                            quote = DailyQuote(
                                                sector_id=sector.id,
                                                quote_date=quote_date,
                                                open_price=float(row['开盘']),
                                                high_price=float(row['最高']),
                                                low_price=float(row['最低']),
                                                close_price=float(row['收盘']),
                                                volume=int(row['成交量']) if '成交量' in row else None,
                                                turnover=float(row['成交额']) if '成交额' in row else None,
                                                price_change=float(row['涨跌额']) if '涨跌额' in row else None,
                                                price_change_pct=float(row['涨跌幅']) if '涨跌幅' in row else None
                                            )
                                            db.session.add(quote)
                                            logger.info(f"创建了板块 {sector_code} 日期 {quote_date} 的新行情数据")

                                    db.session.commit()
                                    logger.info(f"成功将板块 {sector_code} 今天的数据存储到数据库")
                                    # 🔧 修复：更新latest_date为今天的日期（ISO格式）
                                    today_iso = latest_trading_date.strftime('%Y-%m-%d') + 'T00:00:00.000'
                                    update_results['latest_date'] = today_iso
                                    update_results['data_count'] += len(today_data)
                                    logger.info(f"已更新latest_date为: {today_iso}")

                            except Exception as today_db_e:
                                logger.error(f"存储板块 {sector_code} 今天数据到数据库失败: {today_db_e}")
                                db.session.rollback()
                        else:
                            logger.warning(f"未能获取板块 {sector_code} 今天的数据")
                    except Exception as today_e:
                        logger.warning(f"获取板块 {sector_code} 今天数据失败: {today_e}")

                # 最终检查数据是否为最新交易日
                if update_results['latest_date'].replace('-', '') >= latest_trading_date_str:
                    logger.info(f"板块 {sector_code} 数据已是最新交易日 {latest_trading_date_str}")
                else:
                    logger.warning(f"板块 {sector_code} 数据仍然不是最新交易日")
            else:
                logger.warning(f"未能获取板块 {sector_code} 的历史数据")

        except Exception as e:
            logger.error(f"更新板块 {sector_code} 历史数据失败: {e}")

        # 2. 强制刷新该板块的实时数据
        logger.info(f"强制更新板块 {sector_code} 的实时数据")

        try:
            # 清除实时数据缓存
            if data_service.cache_service:
                try:
                    # 清除特定板块的实时数据缓存
                    _force_clear_sector_cache(data_service.cache_service.cache_manager, 'realtime_quotes_cache', sector_code)
                    _force_clear_sector_cache(data_service.cache_service.cache_manager, 'sector_data_cache', sector_code)
                    logger.info(f"已清除板块实时数据缓存")
                except Exception as cache_e:
                    logger.warning(f"清除实时数据缓存失败: {cache_e}")

            # 强制获取最新实时数据
            sector_data = data_service.get_sector_latest_quote(sector_code)
            if sector_data:
                update_results['realtime_data_updated'] = True
                logger.info(f"成功获取板块 {sector_code} 最新实时数据")
            else:
                logger.warning(f"未能获取板块 {sector_code} 的实时数据")

        except Exception as e:
            logger.error(f"更新板块 {sector_code} 实时数据失败: {e}")

        # 3. 重新计算技术分析（包括移动平均线）
        logger.info(f"重新计算板块 {sector_code} 的技术分析和移动平均线")

        try:
            # 关键修复：使用database_service更新技术分析，确保移动平均线计算和存储
            # 修复历史移动平均线数据缺失问题：设置generate_historical=True生成完整历史数据
            analysis_updated = database_service.update_sector_analysis(sector_code, generate_historical=True)
            if analysis_updated:
                update_results['indicators_updated'] = True
                logger.info(f"成功更新板块 {sector_code} 的技术分析（包括移动平均线）到数据库")

                # 验证移动平均线数据是否正确存储（包括历史数据）
                try:
                    from models import Sector, TechnicalAnalysis
                    sector = Sector.get_by_code(sector_code)
                    if sector:
                        # 检查最新移动平均线数据
                        latest_analysis = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                                .order_by(TechnicalAnalysis.analysis_date.desc())\
                                                                .first()
                        if latest_analysis and latest_analysis.ma5:
                            logger.info(f"验证成功：板块 {sector_code} 最新移动平均线 MA5={latest_analysis.ma5:.2f}")
                        else:
                            logger.warning(f"验证失败：板块 {sector_code} 最新移动平均线数据仍为空")

                        # 检查历史移动平均线数据完整性
                        total_analysis_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
                        valid_ma_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                                .filter(TechnicalAnalysis.ma5.isnot(None))\
                                                                .filter(TechnicalAnalysis.ma5 != 0)\
                                                                .count()

                        logger.info(f"验证历史数据：板块 {sector_code} 总技术分析记录 {total_analysis_count} 条，有效移动平均线记录 {valid_ma_count} 条")

                        if valid_ma_count > 1:
                            logger.info(f"✅ 历史移动平均线数据生成成功，覆盖 {valid_ma_count} 个交易日")
                            update_results['historical_ma_generated'] = True
                            update_results['historical_ma_count'] = valid_ma_count
                        else:
                            logger.warning(f"⚠️ 历史移动平均线数据不完整，只有 {valid_ma_count} 条有效记录")
                            update_results['historical_ma_generated'] = False
                            update_results['historical_ma_count'] = valid_ma_count

                except Exception as verify_e:
                    logger.warning(f"验证移动平均线数据失败: {verify_e}")
            else:
                logger.warning(f"板块 {sector_code} 技术分析更新失败")

            # 降级：同时使用indicators_cache_service确保兼容性
            if indicators_cache_service:
                try:
                    # 使缓存失效
                    indicators_cache_service.invalidate_cache(sector_code)

                    # 重新计算技术指标
                    indicators = indicators_cache_service.get_or_calculate_indicators(sector_code)

                    if indicators and indicators.get('trend_judgment') != '数据不足':
                        update_results['indicators'] = indicators
                        logger.info(f"成功重新计算板块 {sector_code} 的缓存技术指标")
                    else:
                        logger.warning(f"板块 {sector_code} 缓存技术指标计算结果不完整")

                except Exception as cache_e:
                    logger.error(f"重新计算板块 {sector_code} 缓存技术指标失败: {cache_e}")

        except Exception as e:
            logger.error(f"重新计算板块 {sector_code} 技术分析失败: {e}")

        # 4. 生成更新结果消息
        success_count = sum([
            update_results['historical_data_updated'],
            update_results['realtime_data_updated'],
            update_results['indicators_updated']
        ])

        if success_count > 0:
            message_parts = []
            if update_results['historical_data_updated']:
                message_parts.append(f"历史数据({update_results['data_count']}条)")
            if update_results['realtime_data_updated']:
                message_parts.append("实时数据")
            if update_results['indicators_updated']:
                message_parts.append("技术指标")

            success_message = f"板块 {sector_code} 更新完成: {', '.join(message_parts)}"

            if update_results['latest_date']:
                success_message += f"，最新数据日期: {update_results['latest_date']}"

            return jsonify({
                'success': True,
                'message': success_message,
                'data': update_results,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': f'板块 {sector_code} 所有数据更新都失败',
                'data': update_results,
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"更新单个板块 {sector_code} 失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/<sector_code>/indicators/update', methods=['POST'])
def update_single_sector_indicators(sector_code: str):
    """仅更新单个板块的技术指标"""
    try:
        logger.info(f"开始更新单个板块技术指标: {sector_code}")

        from services.technical_indicators_cache_service import indicators_cache_service

        if not indicators_cache_service:
            return jsonify({
                'success': False,
                'error': '技术指标缓存服务不可用',
                'timestamp': datetime.now().isoformat()
            }), 500

        # 使缓存失效并重新计算
        indicators_cache_service.invalidate_cache(sector_code)
        indicators = indicators_cache_service.get_or_calculate_indicators(sector_code)

        return jsonify({
            'success': True,
            'message': f'板块 {sector_code} 技术指标更新完成',
            'data': {
                'sector_code': sector_code,
                'indicators': indicators,
                'update_time': datetime.now().isoformat()
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"更新单个板块 {sector_code} 技术指标失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/sectors/indicators/batch-update', methods=['POST'])
def batch_update_sectors_indicators():
    """批量更新所有板块的技术指标"""
    try:
        logger.info("开始批量更新所有板块技术指标")

        from services.data_service import data_service
        from services.technical_indicators_cache_service import indicators_cache_service

        if not indicators_cache_service:
            return jsonify({
                'success': False,
                'error': '技术指标缓存服务不可用',
                'timestamp': datetime.now().isoformat()
            }), 500

        # 获取所有板块数据
        cached_data = data_service.get_cached_sector_data()

        if cached_data.empty:
            return jsonify({
                'success': False,
                'error': '无法获取板块数据，请先更新基础数据',
                'timestamp': datetime.now().isoformat()
            }), 400

        # 批量更新技术指标
        sector_codes = cached_data['板块代码'].tolist()

        # 清空所有技术指标缓存
        indicators_cache_service.invalidate_cache()

        # 预热缓存（重新计算所有技术指标）
        warm_up_result = indicators_cache_service.warm_up_cache(sector_codes)

        return jsonify({
            'success': True,
            'message': f'批量更新技术指标完成',
            'data': {
                'total_sectors': warm_up_result['total'],
                'success_count': warm_up_result['success'],
                'failed_count': warm_up_result['failed'],
                'skipped_count': warm_up_result['skipped'],
                'update_time': datetime.now().isoformat()
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"批量更新技术指标失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/data/retry-failed', methods=['POST'])
def retry_failed_sectors():
    """重试失败的板块更新"""
    try:
        # 获取请求中的失败板块列表
        failed_sectors = request.json.get('failed_sectors', []) if request.json else []

        if not failed_sectors:
            return jsonify({
                'success': False,
                'error': '未提供失败板块列表',
                'timestamp': datetime.now().isoformat()
            }), 400

        success_count = 0
        retry_failed = []

        for sector_code in failed_sectors:
            try:
                # 清理板块代码（移除错误信息）
                clean_code = sector_code.split('(')[0] if '(' in sector_code else sector_code

                logger.info(f"重试更新板块: {clean_code}")

                # 更新行情数据
                if database_service.update_sector_quotes(clean_code, days=7):
                    # 更新技术分析
                    if database_service.update_sector_analysis(clean_code):
                        success_count += 1
                        logger.info(f"✅ {clean_code} 重试成功")
                    else:
                        retry_failed.append(f"{clean_code}(分析失败)")
                else:
                    retry_failed.append(f"{clean_code}(行情失败)")

                # 增加重试间隔
                import time
                time.sleep(2)  # 重试时使用更长间隔

            except Exception as e:
                logger.error(f"重试{clean_code}失败: {e}")
                retry_failed.append(f"{clean_code}({str(e)})")

        return jsonify({
            'success': True,
            'data': {
                'total_sectors': len(failed_sectors),
                'success_count': success_count,
                'failed_count': len(retry_failed),
                'failed_sectors': retry_failed
            },
            'message': f'重试完成，成功 {success_count}/{len(failed_sectors)} 个板块',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"重试失败板块更新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/analysis/trend', methods=['GET'])
def get_trend_analysis():
    """获取趋势分析结果"""
    try:
        trend_direction = request.args.get('direction', None)
        analysis_date = request.args.get('date', None)
        
        if analysis_date:
            analysis_date = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        else:
            analysis_date = datetime.now().date()
        
        # 获取最新的技术分析数据
        query = TechnicalAnalysis.query.filter_by(analysis_date=analysis_date)
        
        if trend_direction:
            query = query.filter_by(trend_direction=trend_direction)
        
        analyses = query.all()
        
        # 按趋势方向分组统计
        trend_stats = {
            'up': [],
            'down': [],
            'sideways': [],
            'unknown': []
        }
        
        for analysis in analyses:
            direction = analysis.trend_direction or 'unknown'
            sector_info = {
                'sector_code': analysis.sector.sector_code,
                'sector_name': analysis.sector.sector_name,
                'trend_strength': float(analysis.trend_strength) if analysis.trend_strength else 0,
                'analysis_date': analysis.analysis_date.isoformat()
            }
            trend_stats[direction].append(sector_info)
        
        # 按趋势强度排序
        for direction in trend_stats:
            trend_stats[direction].sort(key=lambda x: x['trend_strength'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': {
                'analysis_date': analysis_date.isoformat(),
                'trend_stats': trend_stats,
                'summary': {
                    'total_sectors': len(analyses),
                    'up_count': len(trend_stats['up']),
                    'down_count': len(trend_stats['down']),
                    'sideways_count': len(trend_stats['sideways']),
                    'unknown_count': len(trend_stats['unknown'])
                }
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取趋势分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/analysis/oscillation', methods=['GET'])
def get_oscillation_analysis():
    """获取震荡分析结果"""
    try:
        analysis_date = request.args.get('date', None)
        
        if analysis_date:
            analysis_date = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        else:
            analysis_date = datetime.now().date()
        
        # 获取震荡板块
        oscillating_analyses = TechnicalAnalysis.query.filter_by(
            analysis_date=analysis_date,
            is_oscillating=True
        ).all()
        
        non_oscillating_analyses = TechnicalAnalysis.query.filter_by(
            analysis_date=analysis_date,
            is_oscillating=False
        ).all()
        
        oscillating_sectors = []
        for analysis in oscillating_analyses:
            sector_info = {
                'sector_code': analysis.sector.sector_code,
                'sector_name': analysis.sector.sector_name,
                'oscillation_range': float(analysis.oscillation_range) if analysis.oscillation_range else 0,
                'volatility': float(analysis.volatility) if analysis.volatility else None,
                'analysis_date': analysis.analysis_date.isoformat()
            }
            oscillating_sectors.append(sector_info)
        
        # 按震荡幅度排序
        oscillating_sectors.sort(key=lambda x: x['oscillation_range'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': {
                'analysis_date': analysis_date.isoformat(),
                'oscillating_sectors': oscillating_sectors,
                'summary': {
                    'total_sectors': len(oscillating_analyses) + len(non_oscillating_analyses),
                    'oscillating_count': len(oscillating_analyses),
                    'non_oscillating_count': len(non_oscillating_analyses),
                    'oscillating_ratio': len(oscillating_analyses) / (len(oscillating_analyses) + len(non_oscillating_analyses)) * 100 if (len(oscillating_analyses) + len(non_oscillating_analyses)) > 0 else 0
                }
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取震荡分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/analysis/consecutive', methods=['GET'])
def get_consecutive_analysis():
    """获取连续上涨分析"""
    try:
        analysis_date = request.args.get('date', None)
        min_days = request.args.get('min_days', 3, type=int)
        
        if analysis_date:
            analysis_date = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        else:
            analysis_date = datetime.now().date()
        
        # 获取连续上涨板块
        consecutive_up_analyses = TechnicalAnalysis.query.filter(
            TechnicalAnalysis.analysis_date == analysis_date,
            TechnicalAnalysis.consecutive_up_days >= min_days
        ).order_by(TechnicalAnalysis.consecutive_up_days.desc()).all()
        
        # 获取连续下跌板块
        consecutive_down_analyses = TechnicalAnalysis.query.filter(
            TechnicalAnalysis.analysis_date == analysis_date,
            TechnicalAnalysis.consecutive_down_days >= min_days
        ).order_by(TechnicalAnalysis.consecutive_down_days.desc()).all()
        
        consecutive_up_sectors = []
        for analysis in consecutive_up_analyses:
            sector_info = {
                'sector_code': analysis.sector.sector_code,
                'sector_name': analysis.sector.sector_name,
                'consecutive_days': analysis.consecutive_up_days,
                'trend_strength': float(analysis.trend_strength) if analysis.trend_strength else 0,
                'analysis_date': analysis.analysis_date.isoformat()
            }
            consecutive_up_sectors.append(sector_info)
        
        consecutive_down_sectors = []
        for analysis in consecutive_down_analyses:
            sector_info = {
                'sector_code': analysis.sector.sector_code,
                'sector_name': analysis.sector.sector_name,
                'consecutive_days': analysis.consecutive_down_days,
                'trend_strength': float(analysis.trend_strength) if analysis.trend_strength else 0,
                'analysis_date': analysis.analysis_date.isoformat()
            }
            consecutive_down_sectors.append(sector_info)
        
        return jsonify({
            'success': True,
            'data': {
                'analysis_date': analysis_date.isoformat(),
                'min_days': min_days,
                'consecutive_up_sectors': consecutive_up_sectors,
                'consecutive_down_sectors': consecutive_down_sectors,
                'summary': {
                    'up_sectors_count': len(consecutive_up_sectors),
                    'down_sectors_count': len(consecutive_down_sectors)
                }
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取连续涨跌分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/analysis/new-high', methods=['GET'])
def get_new_high_analysis():
    """获取创新高分析"""
    try:
        analysis_date = request.args.get('date', None)
        days = request.args.get('days', 5, type=int)  # 5日、10日、20日、60日新高

        if analysis_date:
            analysis_date = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        else:
            analysis_date = datetime.now().date()

        # 根据天数选择对应的字段
        if days == 5:
            filter_field = TechnicalAnalysis.is_new_high_5d
        elif days == 10:
            filter_field = TechnicalAnalysis.is_new_high_10d
        elif days == 20:
            filter_field = TechnicalAnalysis.is_new_high_20d
        elif days == 60:
            filter_field = TechnicalAnalysis.is_new_high_60d
        else:
            filter_field = TechnicalAnalysis.is_new_high_5d
            days = 5

        # 获取创新高板块
        new_high_analyses = TechnicalAnalysis.query.filter(
            TechnicalAnalysis.analysis_date == analysis_date,
            filter_field == True
        ).all()

        new_high_sectors = []
        for analysis in new_high_analyses:
            sector_info = {
                'sector_code': analysis.sector.sector_code,
                'sector_name': analysis.sector.sector_name,
                'trend_direction': analysis.trend_direction,
                'trend_strength': float(analysis.trend_strength) if analysis.trend_strength else 0,
                'consecutive_up_days': analysis.consecutive_up_days,
                'ma5': float(analysis.ma5) if analysis.ma5 else None,
                'ma20': float(analysis.ma20) if analysis.ma20 else None,
                'analysis_date': analysis.analysis_date.isoformat()
            }
            new_high_sectors.append(sector_info)

        # 按趋势强度排序
        new_high_sectors.sort(key=lambda x: x['trend_strength'], reverse=True)

        return jsonify({
            'success': True,
            'data': {
                'analysis_date': analysis_date.isoformat(),
                'days': days,
                'new_high_sectors': new_high_sectors,
                'summary': {
                    'new_high_count': len(new_high_sectors),
                    'avg_trend_strength': sum(s['trend_strength'] for s in new_high_sectors) / len(new_high_sectors) if new_high_sectors else 0
                }
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取创新高分析失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/data/update', methods=['POST'])
def update_data():
    """智能数据更新 - 根据数据新鲜度智能判断更新策略"""
    try:
        from services.data_service import DataService
        from config import LocalConfig
        from utils.trading_calendar import trading_calendar

        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        # 创建数据服务
        data_service = DataService(mysql_config=mysql_config)

        sector_code = request.json.get('sector_code') if request.json else None
        update_mode = request.json.get('update_mode', 'incremental') if request.json else 'incremental'
        # 🔧 修复：添加用户主动更新标识，在交易时间内强制获取最新数据
        force_user_update = request.json.get('force_user_update', True) if request.json else True

        logger.info(f"开始智能数据更新，模式: {update_mode}, 用户主动更新: {force_user_update}")

        if sector_code:
            # 单个板块更新（保持原有逻辑）
            from services.incremental_update_service import IncrementalUpdateService
            update_service = IncrementalUpdateService(mysql_config=mysql_config)

        else:
            # 全量板块智能更新
            logger.info("开始全量板块智能更新")

            # 🔧 修复：用户主动更新时在交易时间内强制获取最新数据
            from utils.trading_calendar import trading_calendar
            is_trading_time = trading_calendar.is_trading_time()

            # 如果是交易时间且用户主动更新，强制刷新数据
            should_force_refresh = is_trading_time and force_user_update

            logger.info(f"交易时间: {is_trading_time}, 用户主动更新: {force_user_update}, 强制刷新: {should_force_refresh}")

            # 🔧 修复：用户主动更新时，强制更新时间戳
            logger.info(f"智能更新参数: force_refresh={should_force_refresh}, smart_update={not should_force_refresh}")

            # 使用智能更新策略获取最新数据
            updated_data = data_service.get_enhanced_sector_realtime_data_optimized(
                force_refresh=should_force_refresh,
                smart_update=not should_force_refresh  # 如果强制刷新，则不使用智能更新逻辑
            )

            # 🔧 修复：如果是用户主动更新，无论是否强制刷新都要更新时间戳
            if force_user_update and not updated_data.empty:
                import pandas as pd
                current_time = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                updated_data['数据更新时间'] = current_time
                logger.info(f"用户主动更新：强制更新所有板块的数据时间戳为: {current_time}")

                # 🔧 修复：将更新后的数据重新缓存，确保下次读取时能获取到新的时间戳
                if data_service.cache_service:
                    try:
                        data_service.cache_service.set_cache(
                            'sector_data_cache',
                            'enhanced_sector_realtime_data',
                            updated_data,
                            ttl=86400  # 24小时TTL
                        )
                        logger.info("用户主动更新：已将更新时间戳后的数据重新缓存")
                    except Exception as e:
                        logger.error(f"重新缓存更新后数据失败: {e}")

            if not updated_data.empty:
                # 重新计算技术指标
                logger.info("智能更新完成，开始重新计算技术指标")
                updated_count = _recalculate_and_update_indicators(data_service)

                return jsonify({
                    'success': True,
                    'message': f'智能更新完成，更新了{len(updated_data)}个板块，重算了{updated_count}个技术指标',
                    'data': {
                        'total_sectors': len(updated_data),
                        'updated_indicators': updated_count,
                        'update_time': datetime.now().isoformat()
                    },
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '智能更新失败，无法获取板块数据',
                    'timestamp': datetime.now().isoformat()
                }), 500

        if sector_code:
            # 单个板块更新（保持原有逻辑）
            logger.info(f"开始增量更新板块: {sector_code}")

            # 检查缺失的交易日
            missing_dates = update_service.get_missing_trading_dates(sector_code)

            # 检查最后交易日数据新鲜度
            freshness_check = update_service.check_last_trading_day_freshness(sector_code)

            # 执行增量更新
            update_result = update_service.perform_incremental_update(sector_code, missing_dates)

            # 如果最后交易日需要更新，单独处理
            if freshness_check.get('need_update', False):
                last_date = freshness_check.get('last_trading_date')
                if last_date:
                    last_day_result = update_service.perform_incremental_update(sector_code, [last_date])
                    update_result['last_trading_day_updated'] = last_day_result.get('success', False)

            return jsonify({
                'success': update_result.get('success', False),
                'data': {
                    'sector_code': sector_code,
                    'missing_dates_count': len(missing_dates),
                    'updated_count': update_result.get('updated_count', 0),
                    'failed_count': update_result.get('failed_count', 0),
                    'last_trading_day_check': freshness_check,
                    'update_mode': 'incremental'
                },
                'message': f'板块 {sector_code} 增量更新完成: {update_result.get("message", "")}',
                'timestamp': datetime.now().isoformat()
            })
        else:
            # 批量增量更新所有板块（简化版本）
            logger.info("开始批量增量更新所有板块")

            # 这里可以扩展为批量处理逻辑
            # 目前返回提示信息
            return jsonify({
                'success': True,
                'data': {
                    'update_mode': 'incremental_batch',
                    'message': '批量增量更新功能开发中，请使用单板块更新'
                },
                'message': '批量增量更新功能开发中',
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"数据更新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/data/init', methods=['POST'])
def init_data():
    """初始化数据"""
    try:
        # 初始化板块数据
        success = database_service.init_sectors()

        return jsonify({
            'success': success,
            'message': '数据初始化' + ('成功' if success else '失败'),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"数据初始化失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/stats/summary', methods=['GET'])
def get_summary_stats():
    """获取市场概览统计"""
    try:
        analysis_date = request.args.get('date', None)

        if analysis_date:
            analysis_date = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        else:
            analysis_date = datetime.now().date()

        # 获取当日所有分析数据
        analyses = TechnicalAnalysis.query.filter_by(analysis_date=analysis_date).all()

        if not analyses:
            return jsonify({
                'success': False,
                'error': f'未找到 {analysis_date} 的分析数据',
                'timestamp': datetime.now().isoformat()
            }), 404

        # 统计各项指标
        trend_stats = {'up': 0, 'down': 0, 'sideways': 0, 'unknown': 0}
        oscillating_count = 0
        consecutive_up_count = 0
        new_high_5d_count = 0
        new_high_20d_count = 0

        for analysis in analyses:
            # 趋势统计
            direction = analysis.trend_direction or 'unknown'
            trend_stats[direction] += 1

            # 震荡统计
            if analysis.is_oscillating:
                oscillating_count += 1

            # 连续上涨统计（>=3天）
            if analysis.consecutive_up_days >= 3:
                consecutive_up_count += 1

            # 新高统计
            if analysis.is_new_high_5d:
                new_high_5d_count += 1
            if analysis.is_new_high_20d:
                new_high_20d_count += 1

        total_sectors = len(analyses)

        summary = {
            'analysis_date': analysis_date.isoformat(),
            'total_sectors': total_sectors,
            'trend_distribution': {
                'up': {'count': trend_stats['up'], 'percentage': round(trend_stats['up'] / total_sectors * 100, 1)},
                'down': {'count': trend_stats['down'], 'percentage': round(trend_stats['down'] / total_sectors * 100, 1)},
                'sideways': {'count': trend_stats['sideways'], 'percentage': round(trend_stats['sideways'] / total_sectors * 100, 1)},
                'unknown': {'count': trend_stats['unknown'], 'percentage': round(trend_stats['unknown'] / total_sectors * 100, 1)}
            },
            'oscillating': {
                'count': oscillating_count,
                'percentage': round(oscillating_count / total_sectors * 100, 1)
            },
            'consecutive_up': {
                'count': consecutive_up_count,
                'percentage': round(consecutive_up_count / total_sectors * 100, 1)
            },
            'new_highs': {
                '5d': {'count': new_high_5d_count, 'percentage': round(new_high_5d_count / total_sectors * 100, 1)},
                '20d': {'count': new_high_20d_count, 'percentage': round(new_high_20d_count / total_sectors * 100, 1)}
            }
        }

        return jsonify({
            'success': True,
            'data': summary,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取概览统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/system/status', methods=['GET'])
def get_system_status():
    """获取系统状态"""
    try:
        from database import db
        from services.scheduler_service import scheduler_service

        # 检查数据库连接状态
        db_status = "connected"
        db_error = None
        try:
            # 尝试执行简单查询来测试数据库连接
            from sqlalchemy import text
            db.session.execute(text('SELECT 1'))
            db.session.commit()
        except Exception as e:
            db_status = "disconnected"
            db_error = str(e)

        # 获取数据统计
        sectors_count = 0
        quotes_count = 0
        analysis_count = 0
        latest_data_date = None

        try:
            sectors_count = Sector.query.count()
            quotes_count = DailyQuote.query.count()
            analysis_count = TechnicalAnalysis.query.count()

            # 获取最新数据日期
            latest_quote = DailyQuote.query.order_by(DailyQuote.quote_date.desc()).first()
            if latest_quote:
                latest_data_date = latest_quote.quote_date.isoformat()
        except Exception as e:
            logger.warning(f"获取数据统计失败: {e}")

        # 获取调度器状态
        scheduler_status = None
        try:
            scheduler_status = scheduler_service.get_job_status()
        except Exception as e:
            logger.warning(f"获取调度器状态失败: {e}")
            scheduler_status = {"running": False, "error": str(e)}

        # 系统信息（简单实现）
        import os
        system_info = {
            "platform": os.name,
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "status": "running"
        }

        # 尝试获取系统资源信息（如果psutil可用）
        try:
            import psutil
            system_info.update({
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent
            })
        except ImportError:
            # psutil不可用时的备用信息
            system_info.update({
                "cpu_percent": "N/A",
                "memory_percent": "N/A",
                "disk_percent": "N/A"
            })

        status_data = {
            "database": {
                "status": db_status,
                "error": db_error,
                "sectors_count": sectors_count,
                "quotes_count": quotes_count,
                "analysis_count": analysis_count,
                "latest_data_date": latest_data_date
            },
            "scheduler": scheduler_status,
            "system": system_info,
            "api_status": "running",
            "version": "1.0.0"
        }

        return jsonify({
            'success': True,
            'data': status_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/scheduler/status', methods=['GET'])
def get_scheduler_status():
    """获取调度器状态"""
    try:
        from services.scheduler_service import scheduler_service
        status = scheduler_service.get_job_status()

        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@api_bp.route('/scheduler/manual-update', methods=['POST'])
def manual_update():
    """手动触发更新"""
    try:
        from services.scheduler_service import scheduler_service

        sector_code = request.json.get('sector_code') if request.json else None
        result = scheduler_service.manual_update(sector_code)

        return jsonify({
            'success': True,
            'data': result,
            'message': '手动更新已触发',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"手动更新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/sectors/full-reload', methods=['POST'])
def full_reload_sectors_data():
    """完全重载86个板块数据 - 同步处理版本"""

    def generate_progress():
        """生成进度数据的生成器函数"""
        try:
            # 导入必要的模块
            from services.data_service import DataService
            from config import LocalConfig
            import akshare as ak
            import pandas as pd

            # 发送开始信号
            yield f"data: {json.dumps({'type': 'start', 'message': '开始完全重载86个板块数据'})}\n\n"

            # 创建配置
            config = LocalConfig()
            mysql_config = {
                'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
                'CACHE_STRATEGY': config.CACHE_STRATEGY
            }

            # 创建数据服务
            data_service = DataService(mysql_config=mysql_config)

            # 步骤1: 获取86个板块基础数据（使用DataService的重试机制）
            yield f"data: {json.dumps({'type': 'progress', 'step': 1, 'total_steps': 3, 'message': '正在获取86个板块基础数据...'})}\n\n"

            # 步骤1的重试机制
            base_df = None
            max_retries = 3
            retry_delay = 10

            for attempt in range(max_retries):
                try:
                    # 使用DataService的重试机制获取86个板块数据
                    logger.info(f"开始获取86个板块基础数据（第{attempt + 1}次尝试）")

                    if attempt > 0:
                        yield f"data: {json.dumps({'type': 'progress', 'step': 1, 'total_steps': 3, 'message': f'第{attempt + 1}次尝试获取基础数据...'})}\n\n"

                    # 使用DataService的内置重试装饰器
                    @data_service._retry_on_failure
                    def get_base_sectors_data():
                        return ak.stock_board_industry_name_em()

                    base_df = get_base_sectors_data()

                    if base_df.empty:
                        raise Exception("返回空数据")

                    logger.info(f"成功获取86个板块基础数据，共{len(base_df)}个板块")
                    yield f"data: {json.dumps({'type': 'progress', 'step': 1, 'total_steps': 3, 'message': f'成功获取{len(base_df)}个板块基础数据', 'completed': len(base_df), 'total': len(base_df)})}\n\n"
                    break

                except Exception as e:
                    logger.warning(f"第{attempt + 1}次获取86个板块基础数据失败: {e}")

                    if attempt < max_retries - 1:
                        yield f"data: {json.dumps({'type': 'warning', 'message': f'第{attempt + 1}次尝试失败，{retry_delay}秒后重试...'})}\n\n"
                        time.sleep(retry_delay)
                    else:
                        logger.error(f"所有重试都失败，无法获取86个板块基础数据")
                        yield f"data: {json.dumps({'type': 'error', 'message': f'获取基础数据失败（已重试{max_retries}次）: {str(e)}'})}\n\n"
                        return

            if base_df is None or base_df.empty:
                yield f"data: {json.dumps({'type': 'error', 'message': '无法获取86个板块基础数据'})}\n\n"
                return

            # 等待间隔，避免API调用过于频繁
            time.sleep(2)

            # 步骤2: 获取A股实时数据用于增强信息（使用故障转移机制）
            yield f"data: {json.dumps({'type': 'progress', 'step': 2, 'total_steps': 3, 'message': '正在获取A股实时数据...'})}\n\n"

            try:
                logger.info("开始获取A股实时数据（使用故障转移机制）")
                # 使用DataService的故障转移机制
                stock_df = data_service._get_stock_realtime_data_with_fallback()

                if stock_df is None or stock_df.empty:
                    logger.warning("A股实时数据为空，将使用基础板块数据")
                    stock_df = pd.DataFrame()

                logger.info(f"成功获取A股实时数据，共{len(stock_df)}只股票")
                yield f"data: {json.dumps({'type': 'progress', 'step': 2, 'total_steps': 3, 'message': f'成功获取{len(stock_df)}只股票实时数据'})}\n\n"

            except Exception as e:
                logger.warning(f"获取A股实时数据失败: {e}，将使用基础板块数据")
                stock_df = pd.DataFrame()
                yield f"data: {json.dumps({'type': 'progress', 'step': 2, 'total_steps': 3, 'message': '获取A股数据失败，使用基础数据'})}\n\n"

            # 等待间隔
            time.sleep(2)

            # 步骤3: 处理和缓存数据
            yield f"data: {json.dumps({'type': 'progress', 'step': 3, 'total_steps': 3, 'message': '正在处理和缓存数据...'})}\n\n"

            try:
                # 增强板块数据（添加领涨股票详细信息）
                enhanced_data = []
                total_sectors = len(base_df)
                processed_count = 0
                failed_sectors = []

                for idx, (_, row) in enumerate(base_df.iterrows()):
                    try:
                        sector_name = row.get('板块名称', '')
                        leading_stock = row.get('领涨股票', '')

                        # 基础数据
                        sector_dict = {
                            '排名': row.get('排名', idx + 1),
                            '板块名称': sector_name,
                            '板块代码': row.get('板块代码', ''),
                            '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                            '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                            '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                            '总市值': float(row.get('总市值', 0)) if pd.notna(row.get('总市值')) else 0.0,
                            '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                            '上涨家数': int(row.get('上涨家数', 0)) if pd.notna(row.get('上涨家数')) else 0,
                            '下跌家数': int(row.get('下跌家数', 0)) if pd.notna(row.get('下跌家数')) else 0,
                            '领涨股票': leading_stock,
                            '领涨股票-涨跌幅': float(row.get('领涨股票-涨跌幅', 0)) if pd.notna(row.get('领涨股票-涨跌幅')) else 0.0,
                            '数据更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        }

                        # 尝试从A股数据中获取领涨股票的详细信息
                        if not stock_df.empty and leading_stock:
                            try:
                                stock_info = stock_df[stock_df['名称'].str.contains(leading_stock, na=False)]
                                if not stock_info.empty:
                                    stock_row = stock_info.iloc[0]
                                    sector_dict['领涨股票代码'] = stock_row.get('代码', '')
                                    sector_dict['领涨股票价格'] = float(stock_row.get('最新价', 0)) if pd.notna(stock_row.get('最新价')) else 0.0
                                else:
                                    sector_dict['领涨股票代码'] = ''
                                    sector_dict['领涨股票价格'] = 0.0
                            except Exception:
                                sector_dict['领涨股票代码'] = ''
                                sector_dict['领涨股票价格'] = 0.0
                        else:
                            sector_dict['领涨股票代码'] = ''
                            sector_dict['领涨股票价格'] = 0.0

                        # 技术指标字段（暂时设为空，页面加载时实时计算）
                        sector_dict['趋势'] = ''
                        sector_dict['震荡'] = ''
                        sector_dict['连续上涨'] = ''
                        sector_dict['新高'] = ''

                        enhanced_data.append(sector_dict)
                        processed_count += 1

                    except Exception as sector_error:
                        # 单个板块处理失败，记录错误但继续处理其他板块
                        failed_sectors.append(f"{sector_name}({str(sector_error)})")
                        logger.warning(f"处理板块 {sector_name} 失败: {sector_error}")

                    # 发送处理进度
                    if (idx + 1) % 10 == 0 or idx == total_sectors - 1:
                        progress_pct = int((idx + 1) / total_sectors * 100)
                        success_rate = int((processed_count / (idx + 1)) * 100) if idx + 1 > 0 else 100
                        message = f'正在处理板块数据 {idx + 1}/{total_sectors} (成功率: {success_rate}%)'
                        yield f"data: {json.dumps({'type': 'progress', 'step': 3, 'total_steps': 3, 'message': message, 'completed': idx + 1, 'total': total_sectors, 'progress': progress_pct})}\n\n"

                # 检查处理结果
                if processed_count == 0:
                    yield f"data: {json.dumps({'type': 'error', 'message': '没有成功处理任何板块数据'})}\n\n"
                    return

                # 转换为DataFrame并缓存到MySQL
                enhanced_df = pd.DataFrame(enhanced_data)

                # 准备结果摘要
                success_rate = int((processed_count / total_sectors) * 100)
                result_summary = {
                    'total_sectors': total_sectors,
                    'processed_count': processed_count,
                    'failed_count': len(failed_sectors),
                    'success_rate': success_rate
                }

                if data_service.cache_service:
                    try:
                        data_service.cache_service.set_cache(
                            'sector_data_cache',
                            'enhanced_sector_realtime_data',
                            enhanced_df,
                            ttl=86400  # 24小时
                        )
                        logger.info(f"成功缓存{processed_count}个板块数据到MySQL")
                        yield f"data: {json.dumps({'type': 'progress', 'step': 3, 'total_steps': 3, 'message': f'成功缓存{processed_count}个板块数据到MySQL数据库'})}\n\n"
                    except Exception as e:
                        logger.error(f"缓存数据到MySQL失败: {e}")
                        yield f"data: {json.dumps({'type': 'warning', 'message': f'缓存数据失败: {str(e)}，但数据处理已完成'})}\n\n"

                # 发送完成信号，包含详细的结果信息
                if failed_sectors:
                    # 部分成功
                    message = f'完全重载部分完成：成功处理{processed_count}/{total_sectors}个板块数据（成功率{success_rate}%）'
                    if len(failed_sectors) <= 5:
                        message += f'，失败板块：{", ".join(failed_sectors)}'
                    else:
                        message += f'，失败板块：{", ".join(failed_sectors[:5])}等{len(failed_sectors)}个'

                    yield f"data: {json.dumps({'type': 'complete', 'message': message, 'result_summary': result_summary, 'timestamp': datetime.now().isoformat()})}\n\n"
                else:
                    # 完全成功
                    message = f'完全重载成功完成：成功处理全部{processed_count}个板块数据'
                    yield f"data: {json.dumps({'type': 'complete', 'message': message, 'result_summary': result_summary, 'timestamp': datetime.now().isoformat()})}\n\n"

            except Exception as e:
                logger.error(f"处理和缓存数据失败: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': f'处理数据失败: {str(e)}'})}\n\n"

        except Exception as e:
            logger.error(f"完全重载过程中发生错误: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'重载过程发生错误: {str(e)}'})}\n\n"

    # 返回Server-Sent Events响应
    return Response(
        generate_progress(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@api_bp.route('/sectors/<sector_code>/stocks', methods=['GET'])
def get_sector_stocks(sector_code: str):
    """获取板块内的股票列表"""
    try:
        # 获取参数
        use_cache = request.args.get('use_cache', 'true').lower() == 'true'

        logger.info(f"开始获取板块 {sector_code} 的所有股票列表，使用缓存: {use_cache}")

        # 导入必要的模块
        import akshare as ak
        import pandas as pd
        from datetime import date
        from models.sector_stock import SectorStock

        # 申万代码到东财代码的映射
        sw_to_bk_mapping = {
            '801010': 'BK0433',  # 农林牧渔 -> 农牧饲渔
            '801020': 'BK0437',  # 采掘 -> 煤炭行业
            '801030': 'BK1019',  # 化工 -> 化学原料
            '801040': 'BK0479',  # 钢铁 -> 钢铁行业
            '801050': 'BK0478',  # 有色金属 -> 有色金属
            '801080': 'BK1036',  # 电子 -> 半导体
            '801110': 'BK0456',  # 家用电器 -> 家电行业
            '801120': 'BK0438',  # 食品饮料 -> 食品饮料
            '801130': 'BK0436',  # 纺织服装 -> 纺织服装
            '801140': 'BK0440',  # 轻工制造 -> 家用轻工
            '801150': 'BK0465',  # 医药生物 -> 化学制药
            '801160': 'BK0427',  # 公用事业 -> 公用事业
            '801170': 'BK0421',  # 交通运输 -> 铁路公路
            '801180': 'BK0451',  # 房地产 -> 房地产开发
            '801200': 'BK0482',  # 商业贸易 -> 商业百货
            '801210': 'BK0485',  # 休闲服务 -> 旅游酒店
            '801230': 'BK0539',  # 综合 -> 综合行业
        }

        # 获取实际的板块代码
        actual_sector_code = sw_to_bk_mapping.get(sector_code, sector_code)

        # 获取板块名称（从86个板块数据中查找）
        try:
            all_sectors_df = ak.stock_board_industry_name_em()
            sector_info = all_sectors_df[all_sectors_df['板块代码'] == actual_sector_code]

            if sector_info.empty:
                # 如果没找到，尝试使用原始代码
                sector_info = all_sectors_df[all_sectors_df['板块代码'] == sector_code]

            if sector_info.empty:
                return jsonify({
                    'success': False,
                    'error': f'未找到板块代码 {sector_code} 对应的板块信息',
                    'timestamp': datetime.now().isoformat()
                }), 404

            sector_name = sector_info.iloc[0]['板块名称']
            up_stocks = int(sector_info.iloc[0]['上涨家数'])
            down_stocks = int(sector_info.iloc[0]['下跌家数'])

        except Exception as e:
            logger.error(f"获取板块信息失败: {e}")
            return jsonify({
                'success': False,
                'error': f'获取板块信息失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }), 500

        # 检查缓存数据
        today = date.today()
        cached_stocks = []

        if use_cache:
            try:
                cached_stocks = SectorStock.get_latest_by_sector(actual_sector_code)
                if cached_stocks:
                    # 检查缓存数据是否是今天的
                    latest_cache_date = cached_stocks[0].data_date
                    if latest_cache_date == today:
                        logger.info(f"使用缓存数据，板块 {sector_name}，共 {len(cached_stocks)} 只股票")

                        # 转换缓存数据为API格式
                        stocks_list = []
                        flat_stocks = 0

                        for stock in cached_stocks:
                            stock_data = stock.to_api_format()
                            change_pct = stock_data['涨跌幅']

                            if change_pct == 0:
                                flat_stocks += 1

                            # 添加所有股票，不进行筛选
                            stocks_list.append(stock_data)

                        # 按涨跌幅排序（降序，涨幅大的在前）
                        stocks_list.sort(key=lambda x: x['涨跌幅'], reverse=True)

                        for i, stock in enumerate(stocks_list):
                            stock['序号'] = i + 1

                        sector_info_data = {
                            'sector_code': sector_code,
                            'sector_name': sector_name,
                            'total_stocks': len(cached_stocks),
                            'up_stocks': up_stocks,
                            'down_stocks': down_stocks,
                            'flat_stocks': flat_stocks
                        }

                        return jsonify({
                            'success': True,
                            'data': {
                                'stocks': stocks_list,
                                'sector_info': sector_info_data
                            },
                            'debug_info': {
                                'original_code': sector_code,
                                'mapped_code': actual_sector_code,
                                'total_stocks': len(cached_stocks),
                                'displayed_stocks': len(stocks_list),
                                'data_source': 'cache',
                                'cache_date': latest_cache_date.isoformat()
                            },
                            'timestamp': datetime.now().isoformat()
                        })
                    else:
                        logger.info(f"缓存数据过期，最新缓存日期: {latest_cache_date}，今天: {today}")
                else:
                    logger.info(f"板块 {actual_sector_code} 无缓存数据")
            except Exception as e:
                logger.warning(f"读取缓存数据失败: {e}，将从API获取")

        # 从API获取板块成分股
        try:
            logger.info(f"从AkShare API获取板块 {actual_sector_code} 的成分股数据")
            stocks_df = ak.stock_board_concept_cons_em(symbol=actual_sector_code)

            if stocks_df.empty:
                return jsonify({
                    'success': True,
                    'data': {
                        'stocks': [],
                        'sector_info': {
                            'sector_code': sector_code,
                            'sector_name': sector_name,
                            'total_stocks': 0,
                            'up_stocks': up_stocks,
                            'down_stocks': down_stocks,
                            'flat_stocks': 0
                        }
                    },
                    'message': f'板块 {sector_name} 暂无成分股数据',
                    'timestamp': datetime.now().isoformat()
                })

            # 数据处理和筛选
            stocks_list = []
            flat_stocks = 0

            for index, row in stocks_df.iterrows():
                stock_data = {
                    '序号': index + 1,
                    '代码': row.get('代码', ''),
                    '名称': row.get('名称', ''),
                    '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                    '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                    '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                    '成交量': int(row.get('成交量', 0)) if pd.notna(row.get('成交量')) else 0,
                    '成交额': float(row.get('成交额', 0)) if pd.notna(row.get('成交额')) else 0.0,
                    '振幅': float(row.get('振幅', 0)) if pd.notna(row.get('振幅')) else 0.0,
                    '最高': float(row.get('最高', 0)) if pd.notna(row.get('最高')) else 0.0,
                    '最低': float(row.get('最低', 0)) if pd.notna(row.get('最低')) else 0.0,
                    '今开': float(row.get('今开', 0)) if pd.notna(row.get('今开')) else 0.0,
                    '昨收': float(row.get('昨收', 0)) if pd.notna(row.get('昨收')) else 0.0,
                    '量比': float(row.get('量比', 0)) if pd.notna(row.get('量比')) else 0.0,
                    '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                    '市盈率动态': float(row.get('市盈率动态', 0)) if pd.notna(row.get('市盈率动态')) else 0.0,
                    '市净率': float(row.get('市净率', 0)) if pd.notna(row.get('市净率')) else 0.0,
                }

                # 统计股票状态并添加所有股票
                change_pct = stock_data['涨跌幅']
                if change_pct == 0:
                    flat_stocks += 1

                # 添加所有股票，不进行筛选
                stocks_list.append(stock_data)

            # 按涨跌幅排序（降序，涨幅大的在前）
            stocks_list.sort(key=lambda x: x['涨跌幅'], reverse=True)

            # 重新编号
            for i, stock in enumerate(stocks_list):
                stock['序号'] = i + 1

            sector_info_data = {
                'sector_code': sector_code,
                'sector_name': sector_name,
                'total_stocks': len(stocks_df),
                'up_stocks': up_stocks,
                'down_stocks': down_stocks,
                'flat_stocks': flat_stocks
            }

            # 保存到缓存
            try:
                # 准备缓存数据（保存所有股票，不只是筛选后的）
                cache_stocks_data = []
                for index, row in stocks_df.iterrows():
                    cache_stock_data = {
                        '代码': row.get('代码', ''),
                        '名称': row.get('名称', ''),
                        '最新价': float(row.get('最新价', 0)) if pd.notna(row.get('最新价')) else 0.0,
                        '涨跌幅': float(row.get('涨跌幅', 0)) if pd.notna(row.get('涨跌幅')) else 0.0,
                        '涨跌额': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else 0.0,
                        '成交量': int(row.get('成交量', 0)) if pd.notna(row.get('成交量')) else 0,
                        '成交额': float(row.get('成交额', 0)) if pd.notna(row.get('成交额')) else 0.0,
                        '振幅': float(row.get('振幅', 0)) if pd.notna(row.get('振幅')) else 0.0,
                        '最高': float(row.get('最高', 0)) if pd.notna(row.get('最高')) else 0.0,
                        '最低': float(row.get('最低', 0)) if pd.notna(row.get('最低')) else 0.0,
                        '今开': float(row.get('今开', 0)) if pd.notna(row.get('今开')) else 0.0,
                        '昨收': float(row.get('昨收', 0)) if pd.notna(row.get('昨收')) else 0.0,
                        '量比': float(row.get('量比', 0)) if pd.notna(row.get('量比')) else 0.0,
                        '换手率': float(row.get('换手率', 0)) if pd.notna(row.get('换手率')) else 0.0,
                        '市盈率动态': float(row.get('市盈率动态', 0)) if pd.notna(row.get('市盈率动态')) else 0.0,
                        '市净率': float(row.get('市净率', 0)) if pd.notna(row.get('市净率')) else 0.0,
                    }
                    cache_stocks_data.append(cache_stock_data)

                # 批量保存到数据库
                saved_count = SectorStock.bulk_upsert(actual_sector_code, today, cache_stocks_data)
                logger.info(f"成功缓存板块 {sector_name} 的 {saved_count} 只股票数据")

            except Exception as e:
                logger.warning(f"保存缓存数据失败: {e}")

            logger.info(f"成功获取板块 {sector_name} 的股票列表，总计 {len(stocks_df)} 只，筛选后 {len(stocks_list)} 只")

            return jsonify({
                'success': True,
                'data': {
                    'stocks': stocks_list,
                    'sector_info': sector_info_data
                },
                'debug_info': {
                    'original_code': sector_code,
                    'mapped_code': actual_sector_code,
                    'total_stocks': len(stocks_df),
                    'displayed_stocks': len(stocks_list),
                    'data_source': 'api'
                },
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"获取板块成分股失败: {e}")
            return jsonify({
                'success': False,
                'error': f'获取板块成分股失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"获取板块股票列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


def convert_stock_code_to_akshare_format(stock_code):
    """
    将股票代码转换为AkShare个股查询API所需的格式

    Args:
        stock_code: 6位股票代码，如 "000001", "600000", "430000", "200029", "900932"

    Returns:
        str: AkShare格式的股票代码，如 "SZ000001", "SH600000", "BJ430000"
        None: 不支持的股票代码格式
    """
    if len(stock_code) != 6:
        return None

    # 上海主板A股：6开头
    if stock_code.startswith('6'):
        return f"SH{stock_code}"

    # 深圳主板A股：000开头
    # 深圳中小板：002开头
    # 深圳创业板：300开头
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        return f"SZ{stock_code}"

    # 北京股票（新三板精选层/北交所）：4开头、8开头
    elif stock_code.startswith('4') or stock_code.startswith('8'):
        return f"BJ{stock_code}"

    # 深圳B股：200开头
    elif stock_code.startswith('2'):
        return f"SZ{stock_code}"

    # 上海B股：900开头
    elif stock_code.startswith('9'):
        return f"SH{stock_code}"

    # 老三板股票：5开头（但AkShare可能不支持）
    elif stock_code.startswith('5'):
        # 老三板股票通常不在AkShare的个股查询范围内
        logger.debug(f"老三板股票代码 {stock_code} 可能不被AkShare支持")
        return None

    # 其他不支持的格式
    else:
        logger.debug(f"不支持的股票代码格式: {stock_code}")
        return None


def get_stock_realtime_data_akshare(stock_code):
    """
    使用AkShare个股查询API获取单只股票的实时数据

    Args:
        stock_code: 6位股票代码，如 "000001"

    Returns:
        dict: 包含涨跌幅、最新价、涨跌额、更新时间等信息的字典，失败时返回None
    """
    try:
        # 转换股票代码格式
        akshare_code = convert_stock_code_to_akshare_format(stock_code)
        if not akshare_code:
            logger.warning(f"无法转换股票代码格式: {stock_code}")
            return None

        # 调用AkShare API获取个股数据
        stock_data = ak.stock_individual_spot_xq(symbol=akshare_code)

        if stock_data is None or stock_data.empty:
            logger.warning(f"获取股票 {akshare_code} 数据为空")
            return None

        # 将DataFrame转换为字典格式
        stock_dict = dict(zip(stock_data['item'], stock_data['value']))

        # 提取需要的字段
        result = {
            '涨跌幅': float(stock_dict.get('涨幅', 0.0)),  # 涨幅字段 → 涨跌幅
            '最新价': float(stock_dict.get('现价', 0.0)),  # 现价字段 → 最新价
            '涨跌额': float(stock_dict.get('涨跌', 0.0)),  # 涨跌字段 → 涨跌额
            '更新时间': stock_dict.get('时间', ''),        # 时间字段 → 更新时间
            '股票名称': stock_dict.get('名称', ''),        # 名称字段
            '昨收价': float(stock_dict.get('昨收', 0.0)),  # 昨收字段
        }

        logger.debug(f"成功获取股票 {stock_code} 实时数据: 涨跌幅={result['涨跌幅']}%, 最新价={result['最新价']}")
        return result

    except Exception as e:
        logger.warning(f"获取股票 {stock_code} 实时数据失败: {e}")
        return None


@api_bp.route('/stocks/special-data', methods=['POST'])
def get_stocks_special_data():
    """
    批量获取股票的特殊数据表信息（N形待选和大笔买入）以及实时涨跌幅数据

    请求体格式:
    {
        "stock_codes": ["000001", "000002", ...]
    }

    返回格式:
    {
        "success": true,
        "data": {
            "000001": {
                "n_shape_status": {
                    "exists": true,
                    "涨停日期": "20250702",
                    "缩量下跌天数": 1
                },
                "large_buy_status": {
                    "exists": true,
                    "总买入金额": 1234.56,
                    "数量": 5,
                    "总卖出金额": 987.65,
                    "买卖比": 1.25,
                    "总买入占比": 0.05
                },
                "realtime_data": {
                    "涨跌幅": 1.23,
                    "最新价": 12.34,
                    "涨跌额": 0.15,
                    "更新时间": "2025-07-09 11:30:00",
                    "股票名称": "平安银行",
                    "昨收价": 12.19
                }
            },
            ...
        }
    }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'stock_codes' not in data:
            return jsonify({
                'success': False,
                'error': '请求参数错误，需要提供stock_codes数组',
                'timestamp': datetime.now().isoformat()
            }), 400

        stock_codes = data['stock_codes']
        if not isinstance(stock_codes, list) or not stock_codes:
            return jsonify({
                'success': False,
                'error': 'stock_codes必须是非空数组',
                'timestamp': datetime.now().isoformat()
            }), 400

        logger.info(f"批量查询特殊数据表信息，股票数量: {len(stock_codes)}")

        # 构建股票代码映射（支持多种格式）
        # 股票列表中的代码格式：000001, 000002
        # N形待选表中的secID格式：000001.XSHE, 000002.XSHE
        # 大笔买入表中的代码格式：1（数字）, secID格式：000001.XSHE

        result_data = {}

        # 批量查询N形待选数据
        n_shape_data = {}
        if stock_codes:
            # 构建secID格式的查询条件
            secid_conditions = []
            for code in stock_codes:
                # 处理不同的股票代码格式
                if len(code) == 6:  # 6位数字代码
                    if code.startswith('6'):
                        secid_conditions.append(f"'{code}.XSHG'")  # 上海
                    else:
                        secid_conditions.append(f"'{code}.XSHE'")  # 深圳
                elif '.' in code:  # 已经是完整格式
                    secid_conditions.append(f"'{code}'")

            if secid_conditions:
                n_shape_query = f"""
                    SELECT secID, 涨停日期, 缩量下跌天数, 持续缩量, 最后一天收盘价
                    FROM n_shape_candidates
                    WHERE secID IN ({','.join(secid_conditions)})
                """

                n_shape_result = db.session.execute(text(n_shape_query))
                for row in n_shape_result:
                    # 从secID提取基础代码
                    base_code = row[0].split('.')[0] if '.' in row[0] else row[0]
                    n_shape_data[base_code] = {
                        'exists': True,
                        '涨停日期': row[1],
                        '缩量下跌天数': row[2],
                        '持续缩量': row[3],
                        '最后一天收盘价': float(row[4]) if row[4] else None
                    }

        # 批量查询大笔买入数据
        large_buy_data = {}
        if stock_codes:
            # 构建查询条件（支持代码和secID两种格式）
            code_conditions = []
            secid_conditions = []

            for code in stock_codes:
                # 数字代码条件
                code_conditions.append(f"'{code}'")
                code_conditions.append(f"'{int(code)}'")  # 处理去掉前导零的情况

                # secID格式条件
                if len(code) == 6:
                    if code.startswith('6'):
                        secid_conditions.append(f"'{code}.XSHG'")
                    else:
                        secid_conditions.append(f"'{code}.XSHE'")
                elif '.' in code:
                    secid_conditions.append(f"'{code}'")

            all_conditions = code_conditions + secid_conditions

            if all_conditions:
                large_buy_query = f"""
                    SELECT 代码, secID, 总买入金额, 数量, 总卖出金额, 买卖比, 总买入占比
                    FROM large_buy_orders
                    WHERE 代码 IN ({','.join(code_conditions)}) OR secID IN ({','.join(secid_conditions)})
                """

                large_buy_result = db.session.execute(text(large_buy_query))
                for row in large_buy_result:
                    # 确定基础代码
                    base_code = None
                    if row[1] and '.' in row[1]:  # 使用secID
                        base_code = row[1].split('.')[0]
                    elif row[0]:  # 使用代码字段
                        # 处理代码字段，可能是数字或字符串
                        code_str = str(row[0]).zfill(6)  # 补齐到6位
                        base_code = code_str

                    if base_code:
                        large_buy_data[base_code] = {
                            'exists': True,
                            '总买入金额': float(row[2]) if row[2] else 0.0,
                            '数量': int(row[3]) if row[3] else 0,
                            '总卖出金额': float(row[4]) if row[4] else 0.0,
                            '买卖比': float(row[5]) if row[5] else 0.0,
                            '总买入占比': float(row[6]) if row[6] else 0.0
                        }

        # 批量获取实时涨跌幅数据（高性能优化版本）
        logger.info(f"开始获取 {len(stock_codes)} 只股票的实时涨跌幅数据")
        realtime_data = {}
        successful_count = 0

        # 性能优化策略：
        # 1. 限制实时数据获取数量，避免超时
        # 2. 优先获取有特殊数据的股票的实时信息
        # 3. 大幅减少延迟时间

        # 获取有特殊数据的股票代码（优先级更高）
        priority_codes = set()
        for code in stock_codes:
            if code in n_shape_data or code in large_buy_data:
                priority_codes.add(code)

        # 限制实时数据获取数量，最多获取前30只股票的实时数据
        max_realtime_stocks = 30
        codes_to_fetch = list(priority_codes)[:max_realtime_stocks//2]  # 优先股票占一半
        remaining_slots = max_realtime_stocks - len(codes_to_fetch)

        # 添加其他股票到获取列表
        for code in stock_codes:
            if code not in codes_to_fetch and remaining_slots > 0:
                codes_to_fetch.append(code)
                remaining_slots -= 1

        logger.info(f"优化策略：从 {len(stock_codes)} 只股票中选择 {len(codes_to_fetch)} 只获取实时数据")

        # 快速获取实时数据（减少延迟）
        for i, code in enumerate(codes_to_fetch):
            realtime_info = get_stock_realtime_data_akshare(code)
            if realtime_info:
                realtime_data[code] = realtime_info
                successful_count += 1

            # 大幅减少延迟时间，每10只股票暂停一次
            if (i + 1) % 10 == 0:
                time.sleep(0.2)  # 每10只股票暂停0.2秒
            else:
                time.sleep(0.02)  # 单只股票延迟0.02秒

        # 为没有获取实时数据的股票提供默认值
        for code in stock_codes:
            if code not in realtime_data:
                realtime_data[code] = {
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '股票名称': '',
                    '昨收价': None
                }

        logger.info(f"实时数据获取完成，成功: {successful_count}/{len(stock_codes)}")

        # 组装结果数据
        for code in stock_codes:
            result_data[code] = {
                'n_shape_status': n_shape_data.get(code, {'exists': False}),
                'large_buy_status': large_buy_data.get(code, {'exists': False}),
                'realtime_data': realtime_data.get(code, {
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '股票名称': '',
                    '昨收价': None
                })
            }

        logger.info(f"成功查询特殊数据表信息，N形待选匹配: {len(n_shape_data)}, 大笔买入匹配: {len(large_buy_data)}, 实时数据成功: {successful_count}")

        return jsonify({
            'success': True,
            'data': result_data,
            'summary': {
                'total_stocks': len(stock_codes),
                'n_shape_matches': len(n_shape_data),
                'large_buy_matches': len(large_buy_data),
                'realtime_success': successful_count
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取股票特殊数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/concept-analysis', methods=['GET'])
def get_concept_analysis():
    """获取概念分析数据 - 性能优化版本"""
    start_time = time.time()

    try:
        logger.info("开始获取概念分析数据")

        # 先获取基础概念数据
        query = db.text("""
            SELECT
                concept,
                count_current,
                count_previous,
                change_from_previous,
                count_initial,
                change_from_initial,
                created_at,
                updated_at
            FROM zhangting_concept_stats
            ORDER BY count_current DESC, change_from_initial DESC
            LIMIT 100
        """)

        # 直接执行查询，避免额外的处理开销
        result = db.session.execute(query)
        rows = result.fetchall()

        # 优化的数据转换过程，同时查询N型待选股票数量
        concept_data = []
        for row in rows:
            concept_name = row[0]

            # 查询该概念的N型待选股票数量
            n_shape_query = db.text("""
                SELECT COUNT(*) as n_count
                FROM n_shape_candidates
                WHERE 题材 = :concept
                   OR ths概念名称 LIKE :concept_pattern
                   OR em板块名称 LIKE :concept_pattern
            """)

            n_result = db.session.execute(n_shape_query, {
                'concept': concept_name,
                'concept_pattern': f'%{concept_name}%'
            })
            n_shape_count = n_result.fetchone()[0]

            row_dict = {
                'concept': row[0],
                'count_current': row[1],
                'count_previous': row[2],
                'change_from_previous': row[3],
                'count_initial': row[4],
                'change_from_initial': row[5],
                'created_at': row[6].isoformat() if row[6] else None,
                'updated_at': row[7].isoformat() if row[7] else None,
                'n_shape_count': n_shape_count
            }
            concept_data.append(row_dict)

        query_time = (time.time() - start_time) * 1000
        logger.info(f"成功获取{len(concept_data)}条概念分析数据，查询耗时: {query_time:.2f}ms")

        return jsonify({
            'success': True,
            'data': concept_data,
            'total': len(concept_data),
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"获取概念分析数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/concept-stocks/<concept_name>', methods=['GET'])
def get_concept_stocks(concept_name):
    """获取指定概念的涨停股票列表"""
    start_time = time.time()

    try:
        logger.info(f"开始获取概念 '{concept_name}' 的涨停股票数据")

        # URL解码概念名称（处理中文字符）
        import urllib.parse
        decoded_concept = urllib.parse.unquote(concept_name)

        # 查询包含指定概念的所有涨停股票
        query = db.text("""
            SELECT
                secID as stock_code,
                name as stock_name,
                price as limit_up_price,
                consecutive_days,
                increase_pct,
                first_time,
                last_time,
                concept,
                concept_detail,
                reason,
                created_at,
                updated_at
            FROM zhangting
            WHERE concept = :concept_name
            ORDER BY consecutive_days DESC, increase_pct DESC, price DESC
        """)

        result = db.session.execute(query, {'concept_name': decoded_concept})
        rows = result.fetchall()

        # 转换数据格式
        stocks_data = []
        for row in rows:
            stock_dict = {
                'stock_code': row[0],
                'stock_name': row[1],
                'limit_up_price': float(row[2]) if row[2] else 0,
                'consecutive_days': row[3] if row[3] else 0,
                'increase_pct': float(row[4]) if row[4] else 0,
                'first_time': row[5],
                'last_time': row[6],
                'concept': row[7],
                'concept_detail': row[8],
                'reason': row[9],
                'created_at': row[10].isoformat() if row[10] else None,
                'updated_at': row[11].isoformat() if row[11] else None
            }
            stocks_data.append(stock_dict)

        query_time = (time.time() - start_time) * 1000
        logger.info(f"成功获取概念 '{decoded_concept}' 的 {len(stocks_data)} 只涨停股票，查询耗时: {query_time:.2f}ms")

        return jsonify({
            'success': True,
            'data': stocks_data,
            'total': len(stocks_data),
            'concept_name': decoded_concept,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"获取概念 '{concept_name}' 涨停股票数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'concept_name': concept_name,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/concept-n-stocks/<concept_name>', methods=['GET'])
def get_concept_n_stocks(concept_name):
    """获取指定概念的N型待选股票列表"""
    start_time = time.time()

    try:
        logger.info(f"开始获取概念 '{concept_name}' 的N型待选股票数据")

        # URL解码概念名称（处理中文字符）
        import urllib.parse
        decoded_concept = urllib.parse.unquote(concept_name)

        # 查询包含指定概念的所有N型待选股票，关联大笔买入数据
        query = db.text("""
            SELECT
                nsc.secID as stock_code,
                nsc.secShortName as stock_name,
                nsc.涨停日期 as limit_up_date,
                nsc.缩量下跌天数 as decline_days,
                nsc.持续缩量 as continuous_decline,
                nsc.股票名称 as full_stock_name,
                nsc.题材 as theme,
                nsc.题材汇总 as theme_summary,
                nsc.题材_G_ as theme_g,
                nsc.tradeDate as trade_date,
                nsc.ths概念名称 as ths_concept_names,
                nsc.板块名称 as sector_name,
                nsc.em板块名称 as em_sector_names,
                nsc.created_at,
                nsc.updated_at,
                lbo.总买入金额 as large_buy_amount,
                lbo.数量 as large_buy_count,
                lbo.总卖出金额 as large_sell_amount,
                lbo.买卖比 as buy_sell_ratio,
                lbo.总买入占比 as buy_ratio_percent
            FROM n_shape_candidates nsc
            LEFT JOIN large_buy_orders lbo ON (
                nsc.secID = lbo.secID
                OR SUBSTRING_INDEX(nsc.secID, '.', 1) = lbo.代码
            )
            WHERE nsc.题材 = :concept_name
               OR nsc.题材汇总 LIKE :concept_pattern
            ORDER BY nsc.涨停日期 DESC, nsc.缩量下跌天数 DESC
        """)

        result = db.session.execute(query, {
            'concept_name': decoded_concept,
            'concept_pattern': f'%{decoded_concept}%'
        })
        rows = result.fetchall()

        # 转换数据格式（添加大笔买入字段）
        n_stocks_data = []
        stock_codes = []  # 收集股票代码用于获取实时数据

        for row in rows:
            # 从secID提取基础股票代码
            stock_code = row[0]
            if '.' in stock_code:
                base_code = stock_code.split('.')[0]
            else:
                base_code = stock_code

            stock_codes.append(base_code)

            stock_dict = {
                'stock_code': base_code,  # 使用基础代码
                'stock_name': row[1],
                'limit_up_date': row[2],
                'decline_days': row[3] if row[3] else 0,
                'continuous_decline': row[4],
                'full_stock_name': row[5],
                'theme': row[6],
                'theme_summary': row[7],
                'theme_g': row[8],
                'trade_date': row[9],
                'ths_concept_names': row[10],
                'sector_name': row[11],
                'em_sector_names': row[12],
                'created_at': row[13].isoformat() if row[13] else None,
                'updated_at': row[14].isoformat() if row[14] else None,
                # 大笔买入相关字段
                'large_buy_data': {
                    'exists': row[15] is not None,
                    '总买入金额': float(row[15]) if row[15] else 0.0,
                    '数量': int(row[16]) if row[16] else 0,
                    '总卖出金额': float(row[17]) if row[17] else 0.0,
                    '买卖比': float(row[18]) if row[18] else 0.0,
                    '总买入占比': float(row[19]) if row[19] else 0.0
                }
            }
            n_stocks_data.append(stock_dict)

        # 批量获取实时涨跌幅数据
        logger.info(f"开始获取 {len(stock_codes)} 只股票的实时涨跌幅数据")
        realtime_success_count = 0

        for i, stock_dict in enumerate(n_stocks_data):
            stock_code = stock_codes[i]
            realtime_info = get_stock_realtime_data_akshare(stock_code)

            if realtime_info:
                # 添加实时数据到股票字典中
                stock_dict.update({
                    '涨跌幅': realtime_info['涨跌幅'],
                    '最新价': realtime_info['最新价'],
                    '涨跌额': realtime_info['涨跌额'],
                    '更新时间': realtime_info['更新时间'],
                    '昨收价': realtime_info['昨收价']
                })
                realtime_success_count += 1
            else:
                # 如果获取失败，添加空值
                stock_dict.update({
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '昨收价': None
                })

            # 添加小延迟避免API限制
            time.sleep(0.1)

        logger.info(f"实时数据获取完成，成功: {realtime_success_count}/{len(stock_codes)}")

        query_time = (time.time() - start_time) * 1000
        logger.info(f"成功获取概念 '{decoded_concept}' 的 {len(n_stocks_data)} 只N型待选股票，查询耗时: {query_time:.2f}ms，实时数据成功: {realtime_success_count}")

        return jsonify({
            'success': True,
            'data': n_stocks_data,
            'total': len(n_stocks_data),
            'concept_name': decoded_concept,
            'query_time_ms': round(query_time, 2),
            'realtime_success': realtime_success_count,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"获取概念 '{concept_name}' N型待选股票数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'concept_name': concept_name,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500


# ==================== 板块日历API接口 ====================

@api_bp.route('/sector-calendar/rankings', methods=['GET'])
def get_sector_calendar_rankings():
    """
    获取板块日历排名数据

    查询参数:
    - start_date: 开始日期 (YYYY-MM-DD)，可选
    - end_date: 结束日期 (YYYY-MM-DD)，可选
    - date: 指定单个日期 (YYYY-MM-DD)，可选
    - limit: 返回数量限制，默认10

    返回格式:
    {
        "success": true,
        "data": [
            {
                "id": 1,
                "ranking_date": "2025-07-10",
                "sector_code": "BK0001",
                "sector_name": "板块名称",
                "ranking": 1,
                "close_price": 100.50,
                "price_change": 5.20,
                "price_change_pct": 5.45,
                "volume": 1000000,
                "turnover": 100000000.00,
                ...
            }
        ],
        "total": 10,
        "query_params": {...},
        "timestamp": "2025-07-10T15:30:00"
    }
    """
    try:
        start_time = time.time()

        # 获取查询参数
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        date_str = request.args.get('date')
        limit = request.args.get('limit', 10, type=int)

        # 参数验证 - 支持批量导出的大limit值
        logger.info(f"🔍 板块日历API参数验证: limit={limit}")
        if limit > 10000:  # 提高限制到10000，支持批量导出功能
            logger.warning(f"⚠️ limit值过大: {limit} > 10000")
            return jsonify({
                'success': False,
                'error': '返回数量限制不能超过10000',
                'timestamp': datetime.now().isoformat()
            }), 400
        else:
            logger.info(f"✅ limit值验证通过: {limit} <= 10000")

        from services.sector_ranking_service import sector_ranking_service

        # 根据参数类型调用不同的查询方法
        if date_str:
            # 查询指定日期
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                rankings_data = sector_ranking_service.get_rankings_by_date(target_date)
                query_type = 'single_date'
                query_params = {'date': date_str, 'limit': limit}
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '日期格式错误，请使用YYYY-MM-DD格式',
                    'timestamp': datetime.now().isoformat()
                }), 400

        elif start_date_str and end_date_str:
            # 查询日期范围
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

                if start_date > end_date:
                    return jsonify({
                        'success': False,
                        'error': '开始日期不能晚于结束日期',
                        'timestamp': datetime.now().isoformat()
                    }), 400

                rankings_data = sector_ranking_service.get_rankings_by_range(start_date, end_date)
                query_type = 'date_range'
                query_params = {'start_date': start_date_str, 'end_date': end_date_str, 'limit': limit}
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '日期格式错误，请使用YYYY-MM-DD格式',
                    'timestamp': datetime.now().isoformat()
                }), 400

        else:
            # 获取最近7天的排名数据
            from datetime import date, timedelta
            end_date = date.today()
            start_date = end_date - timedelta(days=7)
            rankings_data = sector_ranking_service.get_rankings_by_range(start_date, end_date)
            query_type = 'recent_days'
            query_params = {'start_date': start_date.strftime('%Y-%m-%d'), 'end_date': end_date.strftime('%Y-%m-%d'), 'limit': limit}

        # 应用数量限制
        if limit and len(rankings_data) > limit:
            rankings_data = rankings_data[:limit]

        query_time = (time.time() - start_time) * 1000

        logger.info(f"成功获取板块日历排名数据，查询类型: {query_type}，返回 {len(rankings_data)} 条数据，耗时: {query_time:.2f}ms")

        return jsonify({
            'success': True,
            'data': rankings_data,
            'total': len(rankings_data),
            'query_type': query_type,
            'query_params': query_params,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"获取板块日历排名数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/sector-calendar/collect', methods=['POST'])
def trigger_sector_calendar_collection():
    """
    手动触发板块排名数据收集

    请求体格式:
    {
        "target_date": "2025-07-10",  // 可选，默认为今天
        "top_n": 10                   // 可选，默认为10
    }

    返回格式:
    {
        "success": true,
        "message": "板块排名数据收集成功",
        "data": {
            "target_date": "2025-07-10",
            "top_n": 10,
            "collected_count": 10
        },
        "timestamp": "2025-07-10T15:30:00"
    }
    """
    try:
        start_time = time.time()

        # 获取请求数据
        request_data = request.get_json() or {}
        target_date_str = request_data.get('target_date')
        top_n = request_data.get('top_n', 10)

        # 参数验证
        if top_n < 1 or top_n > 50:
            return jsonify({
                'success': False,
                'error': '排名数量必须在1-50之间',
                'timestamp': datetime.now().isoformat()
            }), 400

        # 解析目标日期
        if target_date_str:
            try:
                target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': '日期格式错误，请使用YYYY-MM-DD格式',
                    'timestamp': datetime.now().isoformat()
                }), 400
        else:
            target_date = date.today()

        # 调用调度器的手动收集方法
        from services.scheduler_service import scheduler_service

        success = scheduler_service.manual_collect_sector_rankings(
            target_date=target_date,
            top_n=top_n
        )

        query_time = (time.time() - start_time) * 1000

        if success:
            logger.info(f"手动触发板块排名数据收集成功，日期: {target_date}，前{top_n}名，耗时: {query_time:.2f}ms")

            return jsonify({
                'success': True,
                'message': f'板块排名数据收集成功',
                'data': {
                    'target_date': target_date.isoformat(),
                    'top_n': top_n,
                    'collected_count': top_n
                },
                'query_time_ms': round(query_time, 2),
                'timestamp': datetime.now().isoformat()
            })
        else:
            logger.error(f"手动触发板块排名数据收集失败，日期: {target_date}，前{top_n}名，耗时: {query_time:.2f}ms")

            return jsonify({
                'success': False,
                'error': '板块排名数据收集失败，请检查日志获取详细信息',
                'data': {
                    'target_date': target_date.isoformat(),
                    'top_n': top_n
                },
                'query_time_ms': round(query_time, 2),
                'timestamp': datetime.now().isoformat()
            }), 500

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"手动触发板块排名数据收集异常: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500


@api_bp.route('/sector-calendar/active-sectors', methods=['GET'])
def get_active_sectors():
    """
    获取连续活跃的板块

    查询参数:
    - days: 查看最近几天，默认7天
    - min_appearances: 最少出现次数，默认3次

    返回格式:
    {
        "success": true,
        "data": [
            {
                "sector_code": "BK0001",
                "sector_name": "板块名称",
                "appearances": 5,
                "avg_ranking": 3.2,
                "best_ranking": 1
            }
        ],
        "total": 5,
        "query_params": {...},
        "timestamp": "2025-07-10T15:30:00"
    }
    """
    try:
        start_time = time.time()

        # 获取查询参数
        days = request.args.get('days', 7, type=int)
        min_appearances = request.args.get('min_appearances', 3, type=int)

        # 参数验证
        if days < 1 or days > 30:
            return jsonify({
                'success': False,
                'error': '查询天数必须在1-30之间',
                'timestamp': datetime.now().isoformat()
            }), 400

        if min_appearances < 1 or min_appearances > days:
            return jsonify({
                'success': False,
                'error': f'最少出现次数必须在1-{days}之间',
                'timestamp': datetime.now().isoformat()
            }), 400

        from services.sector_ranking_service import sector_ranking_service

        # 获取活跃板块数据
        active_sectors = sector_ranking_service.get_active_sectors(
            days=days,
            min_appearances=min_appearances
        )

        query_time = (time.time() - start_time) * 1000
        query_params = {'days': days, 'min_appearances': min_appearances}

        logger.info(f"成功获取连续活跃板块数据，返回 {len(active_sectors)} 个板块，查询参数: {query_params}，耗时: {query_time:.2f}ms")

        return jsonify({
            'success': True,
            'data': active_sectors,
            'total': len(active_sectors),
            'query_params': query_params,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        logger.error(f"获取连续活跃板块数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500
