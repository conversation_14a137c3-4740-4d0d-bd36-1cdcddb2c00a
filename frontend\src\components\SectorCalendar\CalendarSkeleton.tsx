import React from 'react'
import { Card, Row, Col, Skeleton, Space } from 'antd'
import { useTheme } from '../../theme/ThemeProvider'

/**
 * 板块日历骨架屏组件
 * 在数据加载时提供更好的用户体验
 */
const CalendarSkeleton: React.FC = () => {
  const { colorScheme } = useTheme()

  return (
    <div 
      className="sector-calendar-skeleton"
      style={{ 
        padding: '24px',
        background: colorScheme.background,
        minHeight: '100vh'
      }}
    >
      {/* 页面标题骨架 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space align="center">
            <Skeleton.Avatar size="large" />
            <div>
              <Skeleton.Input style={{ width: 120, height: 32 }} active />
              <br />
              <Skeleton.Input style={{ width: 200, height: 16, marginTop: 8 }} active />
            </div>
          </Space>
        </Col>
        <Col>
          <Space>
            <Skeleton.Input style={{ width: 150, height: 16 }} active />
            <Skeleton.Button style={{ width: 100, height: 32 }} active />
          </Space>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 左侧：日历骨架 */}
        <Col xs={24} lg={16}>
          <Card
            style={{
              background: colorScheme.surface,
              borderColor: colorScheme.outline
            }}
            styles={{
              header: {
                background: colorScheme.surfaceVariant,
                borderBottomColor: colorScheme.outline
              }
            }}
            title={
              <Space>
                <Skeleton.Avatar size="small" />
                <Skeleton.Input style={{ width: 100, height: 20 }} active />
                <Skeleton.Avatar size="small" />
              </Space>
            }
          >
            {/* 日历网格骨架 */}
            <div style={{ padding: '16px 0' }}>
              {/* 星期标题行 */}
              <Row gutter={[8, 8]} style={{ marginBottom: '16px' }}>
                {Array.from({ length: 7 }).map((_, index) => (
                  <Col span={3} key={index} style={{ textAlign: 'center' }}>
                    <Skeleton.Input style={{ width: '100%', height: 20 }} active />
                  </Col>
                ))}
              </Row>
              
              {/* 日历日期网格 */}
              {Array.from({ length: 6 }).map((_, weekIndex) => (
                <Row gutter={[8, 8]} key={weekIndex} style={{ marginBottom: '8px' }}>
                  {Array.from({ length: 7 }).map((_, dayIndex) => (
                    <Col span={3} key={dayIndex}>
                      <div
                        style={{
                          height: '60px',
                          background: colorScheme.surfaceVariant,
                          borderRadius: '6px',
                          padding: '8px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between'
                        }}
                      >
                        <Skeleton.Input style={{ width: '60%', height: 12 }} active />
                        <div style={{ display: 'flex', justifyContent: 'center' }}>
                          <Skeleton.Avatar size="small" />
                        </div>
                        <Skeleton.Input style={{ width: '80%', height: 10 }} active />
                      </div>
                    </Col>
                  ))}
                </Row>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧：统计信息骨架 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* 统计卡片骨架 */}
            <Card
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline
              }}
              styles={{
                header: {
                  background: colorScheme.surfaceVariant,
                  borderBottomColor: colorScheme.outline
                }
              }}
              title={
                <Space>
                  <Skeleton.Avatar size="small" />
                  <Skeleton.Input style={{ width: 80, height: 20 }} active />
                </Space>
              }
            >
              <Row gutter={16}>
                {Array.from({ length: 4 }).map((_, index) => (
                  <Col span={12} key={index}>
                    <div style={{ textAlign: 'center' }}>
                      <Skeleton.Input style={{ width: '100%', height: 16, marginBottom: 8 }} active />
                      <Skeleton.Input style={{ width: '60%', height: 24 }} active />
                    </div>
                  </Col>
                ))}
              </Row>
            </Card>

            {/* 活跃板块列表骨架 */}
            <Card
              style={{
                background: colorScheme.surface,
                borderColor: colorScheme.outline
              }}
              styles={{
                header: {
                  background: colorScheme.surfaceVariant,
                  borderBottomColor: colorScheme.outline
                }
              }}
              title={
                <Space>
                  <Skeleton.Avatar size="small" />
                  <Skeleton.Input style={{ width: 100, height: 20 }} active />
                  <Skeleton.Input style={{ width: 80, height: 16 }} active />
                </Space>
              }
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                {Array.from({ length: 6 }).map((_, index) => (
                  <div 
                    key={index}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '12px',
                      background: index % 2 === 0 ? colorScheme.surfaceVariant : 'transparent',
                      borderRadius: '6px'
                    }}
                  >
                    <div style={{ flex: 1 }}>
                      <Skeleton.Input style={{ width: '80%', height: 16, marginBottom: 4 }} active />
                      <Skeleton.Input style={{ width: '60%', height: 12 }} active />
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <Skeleton.Button style={{ width: 40, height: 20, marginBottom: 4 }} active />
                      <br />
                      <Skeleton.Input style={{ width: 50, height: 12 }} active />
                    </div>
                  </div>
                ))}
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* 选中日期详情骨架 */}
      <Row style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card
            style={{
              background: colorScheme.surface,
              borderColor: colorScheme.outline
            }}
            styles={{
              header: {
                background: colorScheme.surfaceVariant,
                borderBottomColor: colorScheme.outline
              }
            }}
            title={
              <Space>
                <Skeleton.Avatar size="small" />
                <Skeleton.Input style={{ width: 150, height: 20 }} active />
                <Skeleton.Button style={{ width: 60, height: 20 }} active />
              </Space>
            }
            extra={
              <Skeleton.Button style={{ width: 80, height: 32 }} active />
            }
          >
            <Row gutter={[16, 16]}>
              {Array.from({ length: 8 }).map((_, index) => (
                <Col xs={24} sm={12} md={8} lg={6} key={index}>
                  <div
                    style={{
                      background: colorScheme.surfaceVariant,
                      borderRadius: '6px',
                      padding: '12px',
                      height: '120px',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between'
                    }}
                  >
                    <div>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '8px'
                      }}>
                        <Skeleton.Button style={{ width: 30, height: 20 }} active />
                        <Skeleton.Input style={{ width: 50, height: 16 }} active />
                      </div>
                      <Skeleton.Input style={{ width: '100%', height: 16 }} active />
                    </div>
                    
                    <div>
                      <Skeleton.Input style={{ width: '80%', height: 12, marginBottom: 4 }} active />
                      <Skeleton.Input style={{ width: '60%', height: 12, marginBottom: 4 }} active />
                      <Skeleton.Input style={{ width: '70%', height: 12 }} active />
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 自定义样式 */}
      <style>{`
        .sector-calendar-skeleton .ant-skeleton-element {
          background: ${colorScheme.surfaceVariant} !important;
        }

        .sector-calendar-skeleton .ant-skeleton-content .ant-skeleton-title {
          background: linear-gradient(90deg, ${colorScheme.surfaceVariant} 25%, ${colorScheme.surface} 50%, ${colorScheme.surfaceVariant} 75%) !important;
        }

        .sector-calendar-skeleton .ant-skeleton-content .ant-skeleton-paragraph > li {
          background: linear-gradient(90deg, ${colorScheme.surfaceVariant} 25%, ${colorScheme.surface} 50%, ${colorScheme.surfaceVariant} 75%) !important;
        }

        .sector-calendar-skeleton .ant-skeleton-avatar {
          background: linear-gradient(90deg, ${colorScheme.surfaceVariant} 25%, ${colorScheme.surface} 50%, ${colorScheme.surfaceVariant} 75%) !important;
        }

        .sector-calendar-skeleton .ant-skeleton-button {
          background: linear-gradient(90deg, ${colorScheme.surfaceVariant} 25%, ${colorScheme.surface} 50%, ${colorScheme.surfaceVariant} 75%) !important;
        }

        .sector-calendar-skeleton .ant-skeleton-input {
          background: linear-gradient(90deg, ${colorScheme.surfaceVariant} 25%, ${colorScheme.surface} 50%, ${colorScheme.surfaceVariant} 75%) !important;
        }
      `}</style>
    </div>
  )
}

export default CalendarSkeleton
